'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/contexts/AuthContext';

export default function LoginPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // 如果已经登录，重定向到管理后台
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/admin/blog');
    }
  }, [isAuthenticated, isLoading, router]);

  // 如果正在加载或已经登录，显示加载状态
  if (isLoading || isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">
            {isAuthenticated ? '正在跳转...' : '加载中...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <LoginForm
      onSuccess={() => {
        router.push('/admin/blog');
      }}
    />
  );
}
