#!/usr/bin/env tsx

/**
 * 自动删除 JSON 文件中所有 Cloudinary 记录 (无需确认)
 */

import fs from 'fs/promises';
import path from 'path';

interface FileRecord {
  id: string;
  storage: string;
  [key: string]: any;
}

async function autoRemoveCloudinaryFiles(): Promise<void> {
  const filePath = path.join(process.cwd(), 'templates/mycms/src/files.json');
  
  try {
    console.log('🔍 读取文件:', filePath);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch {
      console.error('❌ 文件不存在:', filePath);
      return;
    }
    
    // 读取 JSON 文件
    const fileContent = await fs.readFile(filePath, 'utf-8');
    console.log(`📊 文件大小: ${(fileContent.length / 1024 / 1024).toFixed(2)} MB`);
    
    const files: FileRecord[] = JSON.parse(fileContent);
    console.log(`📊 原始文件总数: ${files.length}`);
    
    // 统计不同存储类型的文件
    const storageStats = files.reduce((stats, file) => {
      const storage = file.storage || 'unknown';
      stats[storage] = (stats[storage] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);
    
    console.log('\n📊 存储类型统计:');
    Object.entries(storageStats).forEach(([storage, count]) => {
      console.log(`   ${storage}: ${count} 个文件`);
    });
    
    const cloudinaryCount = storageStats.cloudinary || 0;
    
    if (cloudinaryCount === 0) {
      console.log('\n✅ 没有找到 Cloudinary 文件，无需处理');
      return;
    }
    
    console.log(`\n🗑️  将删除 ${cloudinaryCount} 个 Cloudinary 文件记录...`);
    
    // 创建备份
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    await fs.copyFile(filePath, backupPath);
    console.log(`💾 已创建备份: ${path.basename(backupPath)}`);
    
    // 过滤掉 Cloudinary 文件
    const filteredFiles = files.filter(file => file.storage !== 'cloudinary');
    
    console.log('🔄 正在写入新文件...');
    
    // 写入过滤后的数据
    const newContent = JSON.stringify(filteredFiles, null, 2);
    await fs.writeFile(filePath, newContent, 'utf-8');
    
    // 计算文件大小变化
    const originalSize = fileContent.length;
    const newSize = newContent.length;
    const savedBytes = originalSize - newSize;
    const savedMB = (savedBytes / 1024 / 1024).toFixed(2);
    
    console.log('\n✅ 删除完成！');
    console.log(`📊 删除前: ${files.length} 个文件`);
    console.log(`📊 删除后: ${filteredFiles.length} 个文件`);
    console.log(`🗑️  已删除: ${cloudinaryCount} 个 Cloudinary 文件记录`);
    console.log(`💾 文件大小减少: ${savedMB} MB`);
    console.log(`📁 备份文件: ${backupPath}`);
    
    // 显示剩余存储类型
    const remainingStats = filteredFiles.reduce((stats, file) => {
      const storage = file.storage || 'unknown';
      stats[storage] = (stats[storage] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);
    
    console.log('\n📊 剩余文件存储类型:');
    Object.entries(remainingStats).forEach(([storage, count]) => {
      console.log(`   ${storage}: ${count} 个文件`);
    });
    
  } catch (error: any) {
    console.error('❌ 处理失败:', error.message);
    
    if (error.code === 'ENOENT') {
      console.error('💡 请确保文件路径正确:', filePath);
    } else if (error.name === 'SyntaxError') {
      console.error('💡 JSON 文件格式错误，请检查文件内容');
    }
    
    process.exit(1);
  }
}

async function main(): Promise<void> {
  try {
    console.log('🚀 自动清理 Cloudinary 文件记录...\n');
    await autoRemoveCloudinaryFiles();
    console.log('\n🎉 清理完成！');
  } catch (error: any) {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  main();
}

export { autoRemoveCloudinaryFiles };
