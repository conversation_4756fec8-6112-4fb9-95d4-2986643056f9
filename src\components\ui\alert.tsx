import * as React from "react"
import { cn } from "@/lib/utils"

interface AlertVariants {
  variant?: 'default' | 'destructive';
}

const getAlertClasses = (variant: 'default' | 'destructive' = 'default') => {
  const baseClasses = "relative w-full rounded-lg border p-4";

  switch (variant) {
    case 'destructive':
      return `${baseClasses} border-red-200 bg-red-50 text-red-800`;
    default:
      return `${baseClasses} bg-gray-50 text-gray-800 border-gray-200`;
  }
};

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & AlertVariants
>(({ className, variant = 'default', ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(getAlertClasses(variant), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }
