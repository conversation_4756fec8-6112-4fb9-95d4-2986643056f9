'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Save, Eye, Upload, X } from 'lucide-react';
import { Post } from '@/types/directus-schema';

interface BlogEditorProps {
  post?: Partial<Post>;
  onSave: (postData: Partial<Post>) => Promise<void>;
  onPreview?: (postData: Partial<Post>) => void;
  isLoading?: boolean;
}

export default function BlogEditor({ 
  post, 
  onSave, 
  onPreview, 
  isLoading = false 
}: BlogEditorProps) {
  const [formData, setFormData] = useState<Partial<Post>>({
    title: post?.title || '',
    slug: post?.slug || '',
    description: post?.description || '',
    content: post?.content || '',
    status: post?.status || 'draft',
    ...post
  });
  
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 自动生成 slug
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      
      // 这里应该上传到 Directus，暂时存储文件对象
      setFormData(prev => ({ ...prev, imageFile: file }));
    }
  };

  const removeImage = () => {
    setImagePreview(null);
    setFormData(prev => ({ ...prev, image: null, imageFile: null }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (status?: 'draft' | 'published') => {
    const dataToSave = {
      ...formData,
      status: status || formData.status
    };
    
    await onSave(dataToSave);
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview(formData);
    }
  };

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">
          {post?.id ? '编辑博客' : '写新博客'}
        </h1>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={handlePreview}
            disabled={!formData.title}
          >
            <Eye className="h-4 w-4 mr-2" />
            预览
          </Button>
          
          <Button
            variant="outline"
            onClick={() => handleSubmit('draft')}
            disabled={isLoading || !formData.title}
          >
            <Save className="h-4 w-4 mr-2" />
            保存草稿
          </Button>
          
          <Button
            onClick={() => handleSubmit('published')}
            disabled={isLoading || !formData.title || !formData.content}
          >
            <Save className="h-4 w-4 mr-2" />
            发布
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主编辑区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">标题 *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="输入博客标题..."
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="slug">URL 路径</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="url-path"
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  文章访问地址：/blog/{formData.slug || 'your-slug'}
                </p>
              </div>
              
              <div>
                <Label htmlFor="description">简介</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="简短描述这篇博客..."
                  rows={3}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* 内容编辑 */}
          <Card>
            <CardHeader>
              <CardTitle>内容 *</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="开始写你的博客内容..."
                rows={20}
                className="font-mono text-sm"
              />
              <p className="text-sm text-gray-500 mt-2">
                支持 HTML 标签，如 &lt;h2&gt;、&lt;p&gt;、&lt;strong&gt;、&lt;em&gt;、&lt;a&gt; 等
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 发布设置 */}
          <Card>
            <CardHeader>
              <CardTitle>发布设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published') => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="published">已发布</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 特色图片 */}
          <Card>
            <CardHeader>
              <CardTitle>特色图片</CardTitle>
            </CardHeader>
            <CardContent>
              {imagePreview || formData.image ? (
                <div className="relative">
                  <img
                    src={imagePreview || (typeof formData.image === 'string' ? formData.image : '')}
                    alt="预览"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={removeImage}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500 mb-2">点击上传图片</p>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    选择图片
                  </Button>
                </div>
              )}
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
