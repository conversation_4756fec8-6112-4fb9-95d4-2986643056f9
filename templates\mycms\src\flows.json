[{"id": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "name": "Request Review", "icon": "approval_delegation", "color": null, "description": "Request a content review for publishing.", "status": "active", "trigger": "manual", "accountability": "all", "options": {"collections": ["posts"], "requireConfirmation": true, "confirmationDescription": "Request Review", "fields": [{"field": "editor", "type": "json", "name": "Editor", "meta": {"interface": "collection-item-dropdown", "note": "Who should review this for publishing?", "width": "full", "options": {"selectedCollection": "directus_users", "filter": {"_and": [{"role": {"id": {"_in": ["ef049c8b-546b-4bbc-9cd7-b05d77e58b66", "d70780bd-f3ed-418b-98c2-f5354fd3fa68", "4516009c-8a04-49e4-b4ac-fd4883da6064"]}}}]}}, "required": true}}, {"field": "comments", "type": "text", "name": "Comments", "meta": {"interface": "input-multiline", "note": "Do you have any helpful notes or comments for the editor?", "width": "full"}}]}, "operation": "a16e81d1-4177-443e-a292-786a66faee8a", "date_created": "2025-07-09T03:49:45.902Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "5429ccb0-7e97-4ef5-9d65-2bbcf964f9a6", "name": "Utils -> Render Template", "icon": "picture_in_picture", "color": null, "description": "Utility flow to render a Liquid JS template", "status": "active", "trigger": "operation", "accountability": "all", "options": {"return": "$last"}, "operation": "c846f644-e7c1-43c9-8bb7-81181de0cd60", "date_created": "2025-07-09T03:49:45.998Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "name": "AI Ghostwriter", "icon": "magic_button", "color": null, "description": "Flow to automatically write blog posts and articles for you using AI.", "status": "active", "trigger": "manual", "accountability": "all", "options": {"collections": ["posts"], "requireConfirmation": true, "confirmationDescription": "AI Ghostwriter", "fields": [{"field": "prompt", "type": "text", "name": "Prompt", "meta": {"interface": "input-multiline", "note": "Describe the post you'd like <PERSON> to write for you.", "width": "full"}}, {"field": "voice", "type": "json", "name": "<PERSON><PERSON> Of Voice", "meta": {"interface": "tags", "note": "What tone of voice would you like <PERSON> to write with?", "options": {"presets": ["friendly", "professional", "casual", "pirate"]}}}], "location": "item"}, "operation": "5d667ac5-2594-4f16-a863-3847d8917caa", "date_created": "2025-07-09T03:49:46.071Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "61757ab0-b139-4079-b5eb-4e05bb8142ac", "name": "Form Submissions -> Email Notifications", "icon": "attach_email", "color": null, "description": "Automatically sends any emails configured for a form whenever a new submission is created.", "status": "active", "trigger": "event", "accountability": "all", "options": {"type": "action", "scope": ["items.create"], "collections": ["form_submissions"]}, "operation": "599b80e4-7c74-4496-b243-da198c8613d9", "date_created": "2025-07-09T03:49:46.188Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "69e87d0b-df14-4779-bdc8-abc05f2f1e97", "name": "Utils -> Get Globals", "icon": "globe_uk", "color": null, "description": "This is a utility flow to use in other flows when you need data from the Globals collection.", "status": "active", "trigger": "operation", "accountability": "activity", "options": {"return": "$last"}, "operation": "bb1b1e3f-032e-48b7-b260-1cf3af4a116c", "date_created": "2025-07-09T03:49:46.280Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "7c8732cd-9d9e-44be-a3f6-89c0d42c7687", "name": "Utils -> Send Email", "icon": "send", "color": null, "description": "Utility flow to send a single email. Used in other flows.", "status": "active", "trigger": "operation", "accountability": "all", "options": {"return": "$last"}, "operation": "6efe1572-6cdd-4c80-a063-eb71b7f1e519", "date_created": "2025-07-09T03:49:46.393Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "91c7bf68-a2e2-4056-a73b-be1dc07d59e5", "name": "track", "icon": "bolt", "color": null, "description": null, "status": "active", "trigger": "event", "accountability": "all", "options": {"type": "action", "scope": ["items.create"], "collections": ["article"]}, "operation": null, "date_created": "2025-07-08T05:48:42.071Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "name": "Utils -> Duplicate Page", "icon": "copy_all", "color": null, "description": "Duplicate an entire page, creating a new copy of all the blocks within it.", "status": "active", "trigger": "manual", "accountability": "all", "options": {"collections": ["pages"], "requireConfirmation": true, "confirmationDescription": "Duplicate this Page", "fields": [{"field": "title", "type": "string", "name": "New Page Title", "meta": {"interface": "input", "note": "What would you like the title of the new page to be? If left blank, will just use the existing title and add \"Co<PERSON>\" to the end.", "width": "full"}}, {"field": "permalink", "type": "string", "name": "New Permalink", "meta": {"interface": "input", "note": "What would you like the permalink of the new page to be? If left blank, will just use the existing permalink and add \"-copy to the end.", "width": "full", "options": {"trim": true}}}, {"field": "notice", "type": "alias", "name": "Notice", "meta": {"interface": "presentation-notice", "options": {"text": "Make sure all changes to this page are saved first because it only duplicates saved values. NOTE: This action will duplicate all blocks on this page.", "color": "warning", "icon": "warning"}}}]}, "operation": "1bb8a2e2-c98a-4a52-9224-78b4100ec087", "date_created": "2025-07-09T03:49:46.497Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "c62ceaa5-bd09-4158-8329-98792debc5d5", "name": "Redirects", "icon": "turn_sharp_right", "color": null, "description": "Automatically creates redirects for changed permalinks or slugs from pages or posts.", "status": "active", "trigger": "event", "accountability": "all", "options": {"type": "filter", "scope": ["items.update"], "collections": ["pages", "posts"], "return": null}, "operation": "282f9a77-5519-460e-bb54-4dc6e30b059a", "date_created": "2025-07-09T03:49:46.581Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}, {"id": "d4bbac48-a444-49e0-aedb-9af5273b88df", "name": "AI Image Generator", "icon": "image_search", "color": null, "description": "Generate AI images for your blog posts.", "status": "active", "trigger": "manual", "accountability": "all", "options": {"collections": ["posts"], "requireConfirmation": true, "confirmationDescription": "AI Image Generator", "fields": [{"field": "prompt", "type": "text", "name": "Prompt", "meta": {"interface": "input-rich-text-md", "note": "Describe the image you want to generate. The more descriptive - the better the results (usually 😉).", "options": {"placeholder": "Create a hand-drawn marker style illustration to be used as a featured image for blog posts targeting developers. The illustration should have a whimsical theme, using bold, uneven marker-style lines to create an engaging and eye-catching design. The background should always be white. ", "toolbar": ["heading", "bold", "italic", "strikethrough", "blockquote", "bullist", "numlist", "table", "code", "empty"]}}}, {"field": "colors", "type": "json", "name": "Colors", "meta": {"interface": "list", "options": {"fields": [{"field": "color", "name": "color", "type": "string", "meta": {"field": "color", "width": "half", "type": "string", "interface": "select-color"}}, {"field": "name", "name": "name", "type": "string", "meta": {"field": "name", "width": "half", "type": "string", "note": "What is the name of the color? AI seems to do better when supplying color names along with the hex codes.", "interface": "input"}}, {"field": "type", "name": "type", "type": "string", "meta": {"field": "type", "width": "full", "type": "string", "interface": "select-radio", "options": {"choices": [{"text": "Primary", "value": "primary"}, {"text": "Secondary", "value": "secondary"}, {"text": "Background", "value": "background"}]}}}]}}}, {"field": "aspect_ratio", "type": "string", "name": "Aspect Ratio", "meta": {"interface": "select-radio", "options": {"choices": [{"text": "1:1", "value": "1:1"}, {"text": "16:9", "value": "16:9"}, {"text": "9:16", "value": "9:16"}]}}}], "location": "item"}, "operation": "0cf30253-f9ed-413f-ac27-7a0ecffa9ee4", "date_created": "2025-07-09T03:49:46.692Z", "user_created": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "operations": null}]