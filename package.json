{"name": "simple-cms-nextjs", "version": "1.0.0", "website": "starters-simple-cms-nextjs.vercel.app", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "generate:types": "tsx ./src/lib/directus/generateDirectusTypes.ts", "lint": "next lint", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "dependencies": {"@directus/sdk": "^19.1.0", "@directus/visual-editing": "^1.1.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "dotenv": "^16.4.7", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "p-queue": "^8.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "shadcn": "^2.4.0", "tailwind-merge": "^2.6.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "@next/bundle-analyzer": "15.2.4", "@next/eslint-plugin-next": "15.2.4", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^22.14.0", "directus-sdk-typegen": "^0.2.0", "eslint": "^9.24.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}}