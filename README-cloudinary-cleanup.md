# Cloudinary 文件删除脚本

这个脚本可以删除 Cloudinary 账户中的所有文件和文件夹。

## ⚠️ 重要警告

**此操作不可逆转！一旦删除，所有文件将永久丢失！**

## 安装依赖

首先需要安装必要的依赖包：

```bash
# 使用 npm
npm install cloudinary dotenv

# 或使用 pnpm
pnpm add cloudinary dotenv

# 或使用 yarn
yarn add cloudinary dotenv
```

## 配置 Cloudinary 凭据

### 方法 1: 使用 .env.cloudinary 文件

1. 复制 `.env.cloudinary` 文件并填入您的 Cloudinary 凭据：

```env
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

2. 获取这些凭据：
   - 登录 [Cloudinary 控制台](https://cloudinary.com/console)
   - 在仪表板页面找到 "Account Details" 部分
   - 复制 Cloud Name、API Key 和 API Secret

### 方法 2: 使用环境变量

```bash
export CLOUDINARY_CLOUD_NAME=your_cloud_name
export CLOUDINARY_API_KEY=your_api_key
export CLOUDINARY_API_SECRET=your_api_secret
```

## 使用方法

### 方法 1: 使用 npm 脚本

```bash
npm run delete-cloudinary-files
```

### 方法 2: 直接运行脚本

```bash
npx tsx scripts/delete-cloudinary-files.ts
```

## 脚本功能

1. **安全确认**: 脚本会要求您输入 "DELETE" 来确认操作
2. **批量删除**: 每次处理最多 500 个文件，避免 API 限制
3. **进度显示**: 实时显示删除进度和结果
4. **错误处理**: 如果批量删除失败，会尝试逐个删除
5. **文件夹清理**: 删除完文件后，会清理空文件夹
6. **统计报告**: 显示删除成功和失败的文件数量

## 脚本输出示例

```
🔗 连接到 Cloudinary: your_cloud_name
⚠️  警告: 此操作将删除 Cloudinary 中的所有文件！
⚠️  此操作不可逆转！
确定要继续吗？输入 "DELETE" 确认: DELETE
🚀 开始删除 Cloudinary 文件...
📁 找到 500 个文件，准备删除...
✅ 成功删除 500 个文件
🔄 继续获取更多文件...
📁 找到 234 个文件，准备删除...
✅ 成功删除 234 个文件
📂 开始删除文件夹...
✅ 删除文件夹: uploads
✅ 删除文件夹: thumbnails

🎉 删除完成！
📊 统计信息:
   ✅ 成功删除文件: 734 个
   ❌ 删除失败文件: 0 个
   📂 删除文件夹: 2 个
```

## 注意事项

1. **网络连接**: 确保网络连接稳定，删除大量文件可能需要较长时间
2. **API 限制**: Cloudinary 有 API 调用频率限制，脚本已添加延迟来避免限制
3. **权限要求**: 确保 API 密钥有删除文件的权限
4. **备份建议**: 在删除前确保您有重要文件的备份

## 故障排除

### 常见错误

1. **认证失败**: 检查 Cloudinary 凭据是否正确
2. **权限不足**: 确保 API 密钥有管理权限
3. **网络超时**: 可能是网络问题，可以重新运行脚本
4. **API 限制**: 脚本会自动处理频率限制

### 获取帮助

如果遇到问题，可以：
1. 检查 Cloudinary 控制台的错误日志
2. 确认网络连接正常
3. 验证 API 凭据是否正确且有效

## 文件列表

- `scripts/delete-cloudinary-files.ts` - 主要删除脚本
- `.env.cloudinary` - 环境变量配置文件模板
- `README-cloudinary-cleanup.md` - 此说明文档
