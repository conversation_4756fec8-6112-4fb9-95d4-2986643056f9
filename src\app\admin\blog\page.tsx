'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import BlogList from '@/components/admin/BlogList';
import { AuthGuard } from '@/contexts/AuthContext';
import { Post } from '@/types/directus-schema';
import { toast } from '@/lib/toast';

export default function BlogManagePage() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取博客列表
  const fetchPosts = async () => {
    try {
      const response = await fetch('/api/admin/blog');
      if (response.ok) {
        const data = await response.json();
        setPosts(data);
      } else {
        throw new Error('Failed to fetch posts');
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      toast.error('获取博客列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除博客
  const handleDelete = async (postId: string) => {
    if (!confirm('确定要删除这篇博客吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/blog/${postId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setPosts(posts.filter(post => post.id !== postId));
        toast.success('博客删除成功');
      } else {
        throw new Error('Failed to delete post');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('删除博客失败');
    }
  };

  // 更改博客状态
  const handleStatusChange = async (postId: string, status: 'draft' | 'published') => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const response = await fetch(`/api/admin/blog/${postId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...post,
          status,
          published_at: status === 'published' ? new Date().toISOString() : post.published_at,
        }),
      });

      if (response.ok) {
        const updatedPost = await response.json();
        setPosts(posts.map(p => p.id === postId ? updatedPost : p));
        toast.success(`博客已${status === 'published' ? '发布' : '设为草稿'}`);
      } else {
        throw new Error('Failed to update post status');
      }
    } catch (error) {
      console.error('Error updating post status:', error);
      toast.error('更新博客状态失败');
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AuthGuard>
      <AdminLayout>
        <BlogList
          posts={posts}
          onDelete={handleDelete}
          onStatusChange={handleStatusChange}
        />
      </AdminLayout>
    </AuthGuard>
  );
}
