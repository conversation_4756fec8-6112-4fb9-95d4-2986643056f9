'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { MessageSquare, AlertCircle } from 'lucide-react';
import { Comment } from '@/types/directus-schema';
import CommentForm from './CommentForm';
import CommentList from './CommentList';

interface CommentsSectionProps {
  postId: string;
  commentsEnabled?: boolean;
  initialComments?: Comment[];
}

export default function CommentsSection({ 
  postId, 
  commentsEnabled = true, 
  initialComments = [] 
}: CommentsSectionProps) {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 获取评论列表
  const fetchComments = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/comments?post=${postId}`);
      const data = await response.json();

      if (response.ok) {
        setComments(data);
      } else {
        setError(data.error || '获取评论失败');
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取评论
  useEffect(() => {
    if (postId && initialComments.length === 0) {
      fetchComments();
    }
  }, [postId]);

  // 评论提交成功后刷新列表
  const handleCommentAdded = () => {
    fetchComments();
  };

  if (!commentsEnabled) {
    return (
      <Card className="mt-8">
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">此文章已关闭评论功能</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="mt-8 space-y-6">
      {/* 评论区标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-6 w-6" />
            <span>评论区</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            欢迎在下方留下您的评论和想法。所有评论将在审核后显示。
          </p>
        </CardContent>
      </Card>

      {/* 发表评论表单 */}
      <CommentForm
        postId={postId}
        onSuccess={handleCommentAdded}
      />

      <Separator />

      {/* 评论列表 */}
      <div>
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载评论中...</p>
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={fetchComments}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                重试
              </button>
            </CardContent>
          </Card>
        ) : (
          <CommentList
            comments={comments}
            onCommentAdded={handleCommentAdded}
          />
        )}
      </div>
    </div>
  );
}
