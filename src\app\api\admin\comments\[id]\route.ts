import { NextRequest, NextResponse } from 'next/server';
import { useDirectus } from '@/lib/directus/directus';
import { readItem, updateItem, deleteItem, withToken } from '@directus/sdk';

// 获取单个评论
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { directus } = useDirectus();
    const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;

    if (!adminToken) {
      return NextResponse.json(
        { error: 'Admin token not configured' },
        { status: 500 }
      );
    }

    const comment = await directus.request(
      withToken(adminToken,
        readItem('comments', id, {
          fields: [
            'id',
            'content',
            'author_name',
            'author_email',
            'author_website',
            'author_ip',
            'author_user_agent',
            'status',
            'date_created',
            'date_updated',
            { post: ['id', 'title', 'slug'] },
            { parent: ['id', 'author_name', 'content'] }
          ]
        })
      )
    );

    return NextResponse.json(comment);
  } catch (error) {
    console.error('Error fetching comment:', error);
    return NextResponse.json(
      { error: 'Comment not found' },
      { status: 404 }
    );
  }
}

// 更新评论状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { directus } = useDirectus();
    const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;

    if (!adminToken) {
      return NextResponse.json(
        { error: 'Admin token not configured' },
        { status: 500 }
      );
    }

    const updatedComment = await directus.request(
      withToken(adminToken,
        updateItem('comments', id, {
          status: body.status,
          content: body.content,
        })
      )
    );

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error('Error updating comment:', error);
    return NextResponse.json(
      { error: 'Failed to update comment' },
      { status: 500 }
    );
  }
}

// 删除评论
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { directus } = useDirectus();
    const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;

    if (!adminToken) {
      return NextResponse.json(
        { error: 'Admin token not configured' },
        { status: 500 }
      );
    }

    await directus.request(
      withToken(adminToken, deleteItem('comments', id))
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting comment:', error);
    return NextResponse.json(
      { error: 'Failed to delete comment' },
      { status: 500 }
    );
  }
}
