# 评论系统 Directus 配置指南

本文档介绍如何在 Directus 中正确配置评论系统的权限和用户。

## 📋 **必需的 Directus 配置**

### 1. 创建评论数据表 (Collection)

在 Directus 后台创建 `comments` 数据表，包含以下字段：

```
comments
├── id (UUID, Primary Key)
├── post (Many-to-One → posts)
├── parent (Many-to-One → comments, 可选)
├── content (Text, 必填)
├── author_name (String, 必填)
├── author_email (String, 必填)
├── author_website (String, 可选)
├── status (String, 选项: pending/approved/rejected/spam, 默认: pending)
├── author_ip (String, 可选)
├── author_user_agent (Text, 可选)
├── date_created (DateTime, 自动)
├── date_updated (DateTime, 自动)
├── user_created (Many-to-One → directus_users, 可选)
└── user_updated (Many-to-One → directus_users, 可选)
```

### 2. 更新博客文章表 (posts)

为 `posts` 表添加评论相关字段：

```
posts (现有表)
├── ... (现有字段)
├── comments_enabled (<PERSON><PERSON><PERSON>, 默认: true)
└── comments (One-to-Many → comments)
```

### 3. 创建专用用户和角色

#### 步骤 1: 创建评论提交角色
1. 进入 Directus 后台 → **设置** → **角色和权限**
2. 点击 **创建角色**
3. 角色名称：`Comment Submitter`
4. 描述：`用于前端评论提交的专用角色`

#### 步骤 2: 配置角色权限
为 `Comment Submitter` 角色设置以下权限：

**comments 表权限：**
- ✅ **Create** - 允许创建评论
- ❌ **Read** - 不允许读取（前端通过公共 API 读取已审核评论）
- ❌ **Update** - 不允许更新
- ❌ **Delete** - 不允许删除

**字段权限 (comments)：**
- ✅ `post` - 可写
- ✅ `parent` - 可写
- ✅ `content` - 可写
- ✅ `author_name` - 可写
- ✅ `author_email` - 可写
- ✅ `author_website` - 可写
- ✅ `author_ip` - 可写
- ✅ `author_user_agent` - 可写
- ❌ `status` - 不可写（系统自动设为 pending）
- ❌ `id` - 不可写（系统自动生成）
- ❌ `date_created` - 不可写（系统自动生成）
- ❌ `date_updated` - 不可写（系统自动生成）

#### 步骤 3: 创建专用用户
1. 进入 **用户管理**
2. 点击 **创建用户**
3. 用户信息：
   - 名字：`Comment`
   - 姓氏：`Bot`
   - 邮箱：`<EMAIL>`
   - 角色：`Comment Submitter`
   - 状态：`Active`

#### 步骤 4: 生成静态 Token
1. 编辑刚创建的 `Comment Bot` 用户
2. 滚动到 **Token** 部分
3. 点击 **生成静态 Token**
4. **重要：点击保存按钮！**
5. 复制生成的 Token

### 4. 配置环境变量

将生成的静态 Token 添加到 `.env` 文件：

```env
# 评论系统专用 Token
DIRECTUS_COMMENT_TOKEN=your_generated_static_token_here
```

## 🔒 **安全最佳实践**

### 权限最小化原则
- 评论提交用户只能创建评论，不能读取、更新或删除
- 敏感字段（如 status、id）不允许前端直接设置
- 所有评论默认为 `pending` 状态，需要管理员审核

### Token 管理
- 使用专用的静态 Token，不要使用管理员 Token
- 定期轮换 Token（建议每 6 个月）
- 将 Token 存储在服务器端环境变量中，不要暴露给前端

### 内容安全
- 所有评论内容都会经过 HTML 转义
- 记录评论者的 IP 地址和 User Agent
- 实施评论审核机制

## 🧪 **测试配置**

### 1. 测试评论提交
```bash
curl -X POST https://your-directus-instance.com/items/comments \
  -H "Authorization: Bearer YOUR_COMMENT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "post": "post-uuid-here",
    "content": "测试评论内容",
    "author_name": "测试用户",
    "author_email": "<EMAIL>"
  }'
```

### 2. 验证权限
- 尝试使用评论 Token 读取评论 → 应该失败 (403)
- 尝试使用评论 Token 更新评论 → 应该失败 (403)
- 尝试使用评论 Token 删除评论 → 应该失败 (403)

## 🚨 **常见问题**

### Token 无效错误
- 确保生成静态 Token 后点击了保存按钮
- 检查 Token 是否正确复制到环境变量
- 确认用户状态为 Active

### 权限被拒绝
- 检查角色权限配置
- 确认用户被分配了正确的角色
- 验证字段权限设置

### 评论不显示
- 检查评论状态是否为 `approved`
- 确认前端 API 只获取已审核评论
- 验证博客文章的 `comments_enabled` 字段

## 📚 **相关文档**

- [Directus 权限系统](https://docs.directus.io/configuration/users-roles-permissions/)
- [静态 Token 配置](https://docs.directus.io/reference/authentication/#static-tokens)
- [API 认证](https://docs.directus.io/reference/authentication/)
