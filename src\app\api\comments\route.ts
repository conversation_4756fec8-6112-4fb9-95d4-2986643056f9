import { NextRequest, NextResponse } from 'next/server';
import { useDirectus } from '@/lib/directus/directus';
import { readItems, createItem, withToken } from '@directus/sdk';
import { validateComment, sanitizeCommentContent } from '@/lib/security/commentSecurity';

// 获取评论列表
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const postId = searchParams.get('post');

		if (!postId) {
			return NextResponse.json({ error: 'Post ID is required' }, { status: 400 });
		}

		const { directus } = useDirectus();

		// 使用管理员令牌读取评论（因为需要访问关联字段）
		const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;
		if (!adminToken) {
			return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
		}

		// 获取该文章的所有已审核评论
		const comments = await directus.request(
			withToken(
				adminToken,
				readItems('comments', {
					filter: {
						post: { _eq: postId },
						status: { _eq: 'approved' },
					},
					fields: ['id', 'content', 'author_name', 'author_website', 'status', 'date_created', 'parent'],
					sort: ['date_created'],
				}),
			),
		);

		return NextResponse.json(comments);
	} catch (error) {
		console.error('Error fetching comments:', error);
		return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
	}
}

// 创建新评论
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { directus } = useDirectus();

		// 验证必填字段
		if (!body.post || !body.author_name || !body.author_email || !body.content) {
			return NextResponse.json({ error: '请填写所有必填字段' }, { status: 400 });
		}

		// 获取客户端信息
		const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
		const userAgent = request.headers.get('user-agent') || 'unknown';

		// 安全验证
		const validation = validateComment({
			content: body.content,
			author_name: body.author_name,
			author_email: body.author_email,
			author_website: body.author_website,
			author_ip: clientIP,
		});

		if (!validation.isValid) {
			return NextResponse.json({ error: '数据验证失败', details: validation.errors }, { status: 400 });
		}

		// 如果检测到垃圾评论，自动标记为 spam
		const initialStatus = validation.isSpam ? 'spam' : 'pending';

		// 使用专用的评论提交令牌
		const commentToken = process.env.DIRECTUS_COMMENT_TOKEN;
		if (!commentToken) {
			return NextResponse.json({ error: 'Comment service not configured' }, { status: 500 });
		}

		const newComment = await directus.request(
			withToken(
				commentToken,
				createItem('comments', {
					post: body.post,
					parent: body.parent || null,
					content: sanitizeCommentContent(body.content),
					author_name: body.author_name.trim(),
					author_email: body.author_email.trim(),
					author_website: body.author_website ? body.author_website.trim() : null,
					status: initialStatus, // 根据垃圾检测结果设置状态
					author_ip: clientIP,
					author_user_agent: userAgent,
				}),
			),
		);

		return NextResponse.json(newComment, { status: 201 });
	} catch (error) {
		console.error('Error creating comment:', error);
		return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 });
	}
}
