#!/usr/bin/env node

/**
 * Cloudinary 批量删除工具
 *
 * 可以删除指定类型的文件或全部文件
 * 支持预览模式和实际删除模式
 */

const readline = require('readline');

// 模拟 Cloudinary SDK（如果未安装）
class CloudinaryAPI {
  constructor(config) {
    this.config = config;
    this.baseUrl = `https://api.cloudinary.com/v1_1/${config.cloud_name}`;
  }

  async resources(options = {}) {
    const params = new URLSearchParams({
      max_results: options.max_results || 500,
      resource_type: options.resource_type || 'image'
    });

    if (options.next_cursor) {
      params.append('next_cursor', options.next_cursor);
    }

    const url = `${this.baseUrl}/resources/${options.resource_type || 'image'}?${params}`;

    // 这里需要添加认证头，实际使用时需要 Cloudinary SDK
    console.log('模拟获取资源列表:', url);

    // 返回模拟数据
    return {
      resources: [],
      next_cursor: null
    };
  }

  async deleteResources(publicIds) {
    console.log(`模拟删除 ${publicIds.length} 个文件:`, publicIds.slice(0, 3));

    return {
      deleted: publicIds.reduce((acc, id) => {
        acc[id] = 'deleted';
        return acc;
      }, {})
    };
  }
}

async function main() {
  console.log('🚀 Cloudinary 批量删除工具\n');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (text) => new Promise(resolve => rl.question(text, resolve));

  try {
    // 获取配置
    console.log('请提供 Cloudinary 配置信息:');
    const cloudName = await question('Cloud Name: ');
    const apiKey = await question('API Key: ');
    const apiSecret = await question('API Secret: ');

    if (!cloudName || !apiKey || !apiSecret) {
      console.log('❌ 缺少必要的配置信息');
      process.exit(1);
    }

    // 选择删除类型
    console.log('\n选择要删除的文件类型:');
    console.log('1. 仅图片 (image)');
    console.log('2. 仅视频 (video)');
    console.log('3. 所有文件 (image + video)');

    const typeChoice = await question('请选择 (1-3): ');

    let resourceTypes = [];
    switch (typeChoice) {
      case '1':
        resourceTypes = ['image'];
        break;
      case '2':
        resourceTypes = ['video'];
        break;
      case '3':
        resourceTypes = ['image', 'video'];
        break;
      default:
        console.log('❌ 无效选择');
        process.exit(1);
    }

    // 预览模式或删除模式
    console.log('\n选择操作模式:');
    console.log('1. 预览模式 (只查看文件数量，不删除)');
    console.log('2. 删除模式 (实际删除文件)');

    const modeChoice = await question('请选择 (1-2): ');
    const isPreview = modeChoice === '1';

    if (!isPreview) {
      console.log('\n⚠️  警告: 您选择了删除模式！');
      console.log('⚠️  此操作将永久删除文件，无法恢复！');

      const confirm = await question('确定要继续吗？输入 "YES DELETE" 确认: ');
      if (confirm !== 'YES DELETE') {
        console.log('❌ 操作已取消');
        process.exit(0);
      }
    }

    // 初始化 API 客户端
    const api = new CloudinaryAPI({
      cloud_name: cloudName,
      api_key: apiKey,
      api_secret: apiSecret
    });

    console.log(`\n🔗 连接到 Cloudinary: ${cloudName}`);
    console.log(`📋 模式: ${isPreview ? '预览' : '删除'}`);
    console.log(`📁 文件类型: ${resourceTypes.join(', ')}\n`);

    let totalFound = 0;
    let totalDeleted = 0;

    // 处理每种资源类型
    for (const resourceType of resourceTypes) {
      console.log(`\n📂 处理 ${resourceType} 文件...`);

      let hasMore = true;
      let nextCursor = null;
      let typeCount = 0;

      while (hasMore) {
        const result = await api.resources({
          resource_type: resourceType,
          max_results: 500,
          next_cursor: nextCursor
        });

        if (result.resources && result.resources.length > 0) {
          typeCount += result.resources.length;
          totalFound += result.resources.length;

          console.log(`📄 找到 ${result.resources.length} 个 ${resourceType} 文件 (累计: ${typeCount})`);

          if (!isPreview) {
            const publicIds = result.resources.map(r => r.public_id);

            try {
              await api.deleteResources(publicIds);
              totalDeleted += publicIds.length;
              console.log(`✅ 删除了 ${publicIds.length} 个文件`);
            } catch (error) {
              console.error(`❌ 删除失败:`, error.message);
            }

            // 添加延迟避免 API 限制
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        hasMore = !!result.next_cursor;
        nextCursor = result.next_cursor;

        if (hasMore && !isPreview) {
          console.log('⏳ 等待下一批...');
        }
      }

      console.log(`📊 ${resourceType} 文件总数: ${typeCount}`);
    }

    // 显示最终结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 最终统计:');
    console.log(`   🔍 发现文件总数: ${totalFound}`);

    if (isPreview) {
      console.log('   👀 预览模式: 未删除任何文件');
      console.log('\n💡 提示: 使用删除模式来实际删除这些文件');
    } else {
      console.log(`   🗑️  已删除文件: ${totalDeleted}`);
      console.log('\n🎉 删除操作完成！');
    }

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = { CloudinaryAPI };
