'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Reply, Globe, Calendar, MessageSquare } from 'lucide-react';
import { Comment } from '@/types/directus-schema';
import CommentForm from './CommentForm';

interface CommentListProps {
  comments: Comment[];
  onCommentAdded?: () => void;
}

interface CommentItemProps {
  comment: Comment;
  onCommentAdded?: () => void;
  level?: number;
}

function CommentItem({ comment, onCommentAdded, level = 0 }: CommentItemProps) {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [showReplies, setShowReplies] = useState(true);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">已审核</Badge>;
      case 'pending':
        return <Badge variant="secondary">待审核</Badge>;
      case 'rejected':
        return <Badge variant="destructive">已拒绝</Badge>;
      case 'spam':
        return <Badge variant="outline" className="border-red-300 text-red-800">垃圾评论</Badge>;
      default:
        return null;
    }
  };

  const handleReplySuccess = () => {
    setShowReplyForm(false);
    if (onCommentAdded) {
      onCommentAdded();
    }
  };

  // 限制嵌套层级，避免过深的回复
  const maxLevel = 3;
  const isMaxLevel = level >= maxLevel;

  return (
    <div className={`${level > 0 ? 'ml-8 mt-4' : 'mt-6'}`}>
      <Card className="border-l-4 border-l-blue-200">
        <CardContent className="p-4">
          {/* 评论头部 */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-3">
              {/* 头像 */}
              <div className="flex-shrink-0">
                <div className="h-10 w-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                  {comment.author_name.charAt(0).toUpperCase()}
                </div>
              </div>
              
              {/* 作者信息 */}
              <div>
                <div className="flex items-center space-x-2">
                  {comment.author_website ? (
                    <a
                      href={comment.author_website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                    >
                      <span>{comment.author_name}</span>
                      <Globe className="h-3 w-3" />
                    </a>
                  ) : (
                    <span className="font-medium text-gray-900">{comment.author_name}</span>
                  )}
                  
                  {getStatusBadge(comment.status || 'pending')}
                </div>
                
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(comment.date_created || '')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 评论内容 */}
          <div className="mb-4">
            <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
              {comment.content}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {!isMaxLevel && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReplyForm(!showReplyForm)}
                  className="text-gray-600 hover:text-blue-600"
                >
                  <Reply className="h-4 w-4 mr-1" />
                  回复
                </Button>
              )}
              
              {comment.replies && comment.replies.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReplies(!showReplies)}
                  className="text-gray-600 hover:text-blue-600"
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  {showReplies ? '隐藏' : '显示'} {comment.replies.length} 条回复
                </Button>
              )}
            </div>
          </div>

          {/* 回复表单 */}
          {showReplyForm && (
            <CommentForm
              postId={typeof comment.post === 'string' ? comment.post : comment.post?.id || ''}
              parentId={comment.id}
              onSuccess={handleReplySuccess}
              onCancel={() => setShowReplyForm(false)}
              isReply={true}
            />
          )}
        </CardContent>
      </Card>

      {/* 回复列表 */}
      {showReplies && comment.replies && comment.replies.length > 0 && (
        <div className="mt-4">
          {comment.replies.map((reply) => (
            <CommentItem
              key={typeof reply === 'string' ? reply : reply.id}
              comment={typeof reply === 'string' ? { id: reply } as Comment : reply}
              onCommentAdded={onCommentAdded}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function CommentList({ comments, onCommentAdded }: CommentListProps) {
  // 只显示已审核的评论
  const approvedComments = comments.filter(comment => comment.status === 'approved');
  
  // 只显示顶级评论（没有父评论的）
  const topLevelComments = approvedComments.filter(comment => !comment.parent);

  if (topLevelComments.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">还没有评论，来发表第一条评论吧！</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <MessageSquare className="h-5 w-5 text-gray-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          评论 ({topLevelComments.length})
        </h3>
      </div>
      
      {topLevelComments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          onCommentAdded={onCommentAdded}
        />
      ))}
    </div>
  );
}
