[{"id": "0bc3b73f-1854-4938-a432-6b86519292b8", "name": "Read Posts", "icon": "badge", "description": "Read Posts", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "1fecfe1c-9615-49da-8d46-c1ae480406ac", "name": "Administrator", "icon": "verified", "description": "$t:admin_description", "ip_access": null, "enforce_tfa": false, "admin_access": true, "app_access": true}, {"id": "3ba01681-3a5f-4fe3-8e95-4706b39f7dc5", "name": "Content - Live Preview", "icon": "preview", "description": "Grants read access to all pages and posts. Used for Live Preview so you can preview draft content. Be careful not to expose tokens of users with this policy.", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "52598a64-071d-4071-96fa-4b620d6189b5", "name": "Team - App Access", "icon": "badge", "description": "Grants Directus Studio access to team members", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": true}, {"id": "8ba4ed6f-d330-4675-ae46-119c533a0928", "name": "Content - Manage", "icon": "edit", "description": "Grants ability to read and edit ALL content.", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "a15b697e-29d5-4164-8d24-eb228ba901e3", "name": "Content - Self", "icon": "self_improvement", "description": "Grants user ability to ONLY edit their own content. Cannot publish content.", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "abf8a154-5b1c-4a46-ac9c-7300570f4f17", "name": "$t:public_label", "icon": "public", "description": "$t:public_description", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "ee1055a2-7c03-4b0b-9b65-ca68491b6329", "name": "Forms - Submission", "icon": "smart_button", "description": "Grants ability to create form submissions. Used in frontend.", "ip_access": null, "enforce_tfa": false, "admin_access": false, "app_access": false}, {"id": "ef049c8b-546b-4bbc-9cd7-b05d77e58b66", "name": "Administrator", "icon": "verified", "description": "$t:admin_policy_description", "ip_access": null, "enforce_tfa": false, "admin_access": true, "app_access": true}]