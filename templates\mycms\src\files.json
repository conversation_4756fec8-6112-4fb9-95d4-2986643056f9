[{"id": "03a7d1c7-81e2-432f-9561-9df2691189c8", "storage": "s3", "filename_disk": "03a7d1c7-81e2-432f-9561-9df2691189c8.png", "filename_download": "03a7d1c7-81e2-432f-9561-9df2691189c8.png", "title": "Directus E-Commerce T-Shirt Product Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:50.160Z", "modified_by": null, "modified_on": "2025-07-09T03:48:51.213Z", "charset": null, "filesize": "659420", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus admin interface showing a product detail page for a 'Directus Super Soft T-Shirt'. The interface displays product variants with color and size options, and a thumbnail image of a person wearing the gray t-shirt with Directus logo.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:51.202Z"}, {"id": "12e02b82-b4a4-4aaf-8ca4-e73c20a41c26", "storage": "s3", "filename_disk": "12e02b82-b4a4-4aaf-8ca4-e73c20a41c26.jpeg", "filename_download": "12e02b82-b4a4-4aaf-8ca4-e73c20a41c26.jpeg", "title": "Cottontail Rabbit in Grass", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:50.929Z", "modified_by": null, "modified_on": "2025-07-09T03:48:51.694Z", "charset": null, "filesize": "214070", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Close-up portrait of a wild cottontail rabbit sitting alert in green grass with a soft-focus golden yellow background. The rabbit is looking directly at the camera with its distinctive large ears upright.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:51.690Z"}, {"id": "14198481-6980-4973-bc9c-1d18b76d1455", "storage": "s3", "filename_disk": "14198481-6980-4973-bc9c-1d18b76d1455.png", "filename_download": "generation-da80f8a3-4fbf-4ad8-8ef5-ba1cf9a06216.png", "title": "Generation Da80f8a3 4fbf 4ad8 8ef5 Ba1cf9a06216.png", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:40:58.488Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T14:43:18.885Z", "charset": null, "filesize": "750945", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "14594872-a805-4251-8dfd-b93bb2effbc0", "storage": "s3", "filename_disk": "14594872-a805-4251-8dfd-b93bb2effbc0.png", "filename_download": "14594872-a805-4251-8dfd-b93bb2effbc0.png", "title": "Frontend Developer Avatar", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:50.493Z", "modified_by": null, "modified_on": "2025-07-09T03:48:52.182Z", "charset": null, "filesize": "1406914", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital avatar representing a frontend developer character in portrait orientation. The image features a stylized character design suitable for profile or user representation in a development or tech context.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:52.179Z"}, {"id": "1d3d2bd3-ff59-4626-bef5-9d5eef6510b3", "storage": "s3", "filename_disk": "1d3d2bd3-ff59-4626-bef5-9d5eef6510b3.png", "filename_download": "1d3d2bd3-ff59-4626-bef5-9d5eef6510b3.png", "title": "Visual Studio Code Logo", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:49.891Z", "modified_by": null, "modified_on": "2025-07-09T03:48:50.512Z", "charset": null, "filesize": "57617", "width": 1024, "height": 1024, "duration": null, "embed": null, "description": "The official blue Visual Studio Code (VS Code) logo, featuring a stylized folded ribbon design forming a visual representation of the '<>' characters in blue gradient on a transparent background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:50.509Z"}, {"id": "1db78159-5dcf-465a-b3c0-8100e0a1d7b5", "storage": "s3", "filename_disk": "1db78159-5dcf-465a-b3c0-8100e0a1d7b5.mp4", "filename_download": "guochan2048.com -2020_08_12_02_02_IMG_1771.mp4", "title": "Guochan2048.com  2020 08 12 02 02 Img 1771", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T02:01:09.601Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T02:01:47.354Z", "charset": null, "filesize": "21408821", "width": null, "height": null, "duration": null, "embed": null, "description": "肉丝\n\nnude stocking", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T02:01:21.286Z"}, {"id": "1fa9bc10-7b43-4b21-97a7-2b6bd758f1b7", "storage": "s3", "filename_disk": "1fa9bc10-7b43-4b21-97a7-2b6bd758f1b7.mp4", "filename_download": "document_6107067397510793232.mp4", "title": "slave.mp4", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T04:03:49.916Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-09T04:04:30.546Z", "charset": null, "filesize": "22989745", "width": null, "height": null, "duration": null, "embed": null, "description": "white stocking", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "204db3cc-e9a2-41f9-affb-7b8a086f2b3e", "storage": "s3", "filename_disk": "204db3cc-e9a2-41f9-affb-7b8a086f2b3e.mp4", "filename_download": "更多视频请在tg收藏夹输入@AnchorPorn (2).mp4", "title": "2023@anchor Porn (2).mp4", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:08:55.090Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T03:24:46.090Z", "charset": null, "filesize": "299182168", "width": null, "height": null, "duration": null, "embed": null, "description": "white stocking", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "2b4a0ba0-52c7-4e10-b191-c803d8da6a36", "storage": "s3", "filename_disk": "2b4a0ba0-52c7-4e10-b191-c803d8da6a36.png", "filename_download": "2b4a0ba0-52c7-4e10-b191-c803d8da6a36.png", "title": "<PERSON><PERSON> - Purple Background", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:51.211Z", "modified_by": null, "modified_on": "2025-07-09T03:48:51.750Z", "charset": null, "filesize": "43613", "width": 512, "height": 512, "duration": null, "embed": null, "description": "The Directus logo displaying a white silhouette of a running gazelle or antelope on a vibrant purple square background with rounded corners. The logo represents speed and agility, core values of the Directus platform.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:51.746Z"}, {"id": "35a67f1b-d401-4300-a503-b8e583186f2a", "storage": "s3", "filename_disk": "35a67f1b-d401-4300-a503-b8e583186f2a.svg", "filename_download": "35a67f1b-d401-4300-a503-b8e583186f2a.svg", "title": "Directus Logo SVG", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:51.365Z", "modified_by": null, "modified_on": "2025-07-09T03:48:51.594Z", "charset": null, "filesize": "11267", "width": null, "height": null, "duration": null, "embed": null, "description": "The scalable vector graphic (SVG) version of the Directus logo featuring a stylized gazelle silhouette. This vector format allows the logo to be scaled to any size without losing quality.", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:51.587Z"}, {"id": "36ab3f04-1dd7-4eca-a653-3d609c0eb2ab", "storage": "s3", "filename_disk": "36ab3f04-1dd7-4eca-a653-3d609c0eb2ab.mp4", "filename_download": "guochan2048.com -2020_08_12_02_06_IMG_1775.mp4", "title": "Guochan2048.com  2020 08 12 02 06 Img 1775", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T02:01:15.176Z", "modified_by": null, "modified_on": "2025-07-09T02:01:33.719Z", "charset": null, "filesize": "58072697", "width": null, "height": null, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T02:01:33.717Z"}, {"id": "3eff7dc2-445a-47c5-9503-3f600ecdb5c6", "storage": "s3", "filename_disk": "3eff7dc2-445a-47c5-9503-3f600ecdb5c6.jpeg", "filename_download": "3eff7dc2-445a-47c5-9503-3f600ecdb5c6.jpeg", "title": "Steampunk Rabbit Portrait", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.946Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.461Z", "charset": null, "filesize": "51166", "width": 512, "height": 512, "duration": null, "embed": null, "description": "Digital art portrait of an anthropomorphized rabbit wearing Victorian or steampunk-style clothing with mechanical details. The rabbit has a realistic head with dark fur and alert ears, while wearing a formal brown jacket with gear elements against a vintage paper background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.454Z"}, {"id": "436424d8-a5b7-4a96-924b-ba0718ee171e", "storage": "s3", "filename_disk": "436424d8-a5b7-4a96-924b-ba0718ee171e.mp4", "filename_download": "irllsljjjjdddd1_1080x1920 (8).mp4", "title": "smoke_2_girls.mp4", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T08:48:32.143Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-09T14:22:25.641Z", "charset": null, "filesize": "107751293", "width": null, "height": null, "duration": null, "embed": null, "description": "2_girls", "location": null, "tags": ["stocking"], "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "43ddd7b8-9b2f-4aa1-b63c-933b4ae81ca2", "storage": "s3", "filename_disk": "43ddd7b8-9b2f-4aa1-b63c-933b4ae81ca2.svg", "filename_download": "43ddd7b8-9b2f-4aa1-b63c-933b4ae81ca2.svg", "title": "<PERSON><PERSON>", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:50.990Z", "modified_by": null, "modified_on": "2025-07-09T03:48:51.233Z", "charset": null, "filesize": "11267", "width": null, "height": null, "duration": null, "embed": null, "description": "The SVG version of the Directus logo designed for use on light backgrounds, featuring the iconic gazelle silhouette in a dark color scheme. This scalable vector format ensures the logo maintains crisp quality at any size.", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:51.230Z"}, {"id": "44a4e780-d59b-4fa5-9b26-1c4b447474d2", "storage": "s3", "filename_disk": "44a4e780-d59b-4fa5-9b26-1c4b447474d2.jpg", "filename_download": "44a4e780-d59b-4fa5-9b26-1c4b447474d2.jpg", "title": "Multicolored Can Wall Art - Dreams Come True", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:52.118Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.936Z", "charset": null, "filesize": "1221311", "width": 1917, "height": 1918, "duration": null, "embed": null, "description": "Creative wall art made from painted beverage cans arranged to form the inspirational phrase \"I FEEL LIKE MAKIN' DREAMS COME TRUE\". The cans are painted in white, yellow, teal, and black with letters stenciled on them, mounted against a textured concrete background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.933Z"}, {"id": "4d1c7893-c5e5-4901-8bc7-0ecc246fa713", "storage": "s3", "filename_disk": "4d1c7893-c5e5-4901-8bc7-0ecc246fa713.png", "filename_download": "BDj61CzArizYI-62p3mVf_65c9bff8f5844da59fac46bae56df11e.png", "title": "B Dj61 Cz Ariz Yi 62p3m Vf 65c9bff8f5844da59fac46bae56df11e.png", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:42:15.781Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T14:43:18.885Z", "charset": null, "filesize": "626266", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "50570a31-a990-453c-bdfc-0ad7175dd8bf", "storage": "s3", "filename_disk": "50570a31-a990-453c-bdfc-0ad7175dd8bf.png", "filename_download": "50570a31-a990-453c-bdfc-0ad7175dd8bf.png", "title": "Directus CRM Pipeline Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:51.951Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.063Z", "charset": null, "filesize": "409799", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of a Directus CRM interface showing a sales pipeline view with three columns: New, Qualification, and Proposal. The pipeline displays ongoing deals with client names and dates, including HealthPlus Digital Transformation, Tezla Deal, SolarTech Solutions, and TechNova AI Integration.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.060Z"}, {"id": "5e93050a-6f17-4314-a7e5-f78bda425fea", "storage": "s3", "filename_disk": "5e93050a-6f17-4314-a7e5-f78bda425fea.png", "filename_download": "5e93050a-6f17-4314-a7e5-f78bda425fea.png", "title": "Content Blocks Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:52.258Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.681Z", "charset": null, "filesize": "828178", "width": 2076, "height": 1551, "duration": null, "embed": null, "description": "Screenshot of a website builder interface showing content blocks functionality against a purple gradient background. The interface displays existing blocks (Hero, Rich Text, Gallery, Pricing, Form) with their content previews and a section below to create new content block types with associated icons.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.678Z"}, {"id": "5f35b7e3-0357-47c3-807f-f132cca95e3f", "storage": "s3", "filename_disk": "5f35b7e3-0357-47c3-807f-f132cca95e3f.png", "filename_download": "5f35b7e3-0357-47c3-807f-f132cca95e3f.png", "title": "Content Writer <PERSON><PERSON>", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:52.370Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.937Z", "charset": null, "filesize": "1194631", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital illustration of a content writer avatar in portrait orientation. The image features a stylized character designed to represent a professional writer or content creator, suitable for profile pictures or user representations in writing or publishing platforms.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.934Z"}, {"id": "6464e61f-455a-4b47-b623-bb12e5251dfe", "storage": "s3", "filename_disk": "6464e61f-455a-4b47-b623-bb12e5251dfe.jpeg", "filename_download": "6464e61f-455a-4b47-b623-bb12e5251dfe.jpeg", "title": "Knight Rabbit in Armor", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:51.896Z", "modified_by": null, "modified_on": "2025-07-09T03:48:52.295Z", "charset": null, "filesize": "537459", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital fantasy art of an anthropomorphic rabbit in detailed plate armor with a purple cape and red accent fabric. The rabbit has distinctive long ears and is portrayed in a noble, knight-like pose with intricate metallic armor against a soft-focus sunset background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:52.292Z"}, {"id": "64cff9bd-eadb-4bc9-831a-781fb8b91806", "storage": "s3", "filename_disk": "64cff9bd-eadb-4bc9-831a-781fb8b91806.png", "filename_download": "full-body_shot_of_beautiful_girl_detailed_face_l-1744270973550.png", "title": "Full Body Shot of Beautiful Girl Detailed Face L 1744270973550.png", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:42:31.143Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T14:43:18.885Z", "charset": null, "filesize": "3364588", "width": 1792, "height": 2368, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "68103296-6634-4d66-918a-04b09afe6621", "storage": "s3", "filename_disk": "68103296-6634-4d66-918a-04b09afe6621.jpeg", "filename_download": "68103296-6634-4d66-918a-04b09afe6621.jpeg", "title": "Black Rabbit Figurine", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:52.549Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.706Z", "charset": null, "filesize": "421019", "width": 3072, "height": 2048, "duration": null, "embed": null, "description": "3D render of a stylized black rabbit figurine with red inner ears, golden eyes, and a leather collar with a medallion. The figurine is illuminated against a blurred nighttime urban background with warm lighting, creating dramatic highlights on its glossy surface.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.700Z"}, {"id": "6964d750-1c00-4b9c-81e4-0c81cfa82bbb", "storage": "s3", "filename_disk": "6964d750-1c00-4b9c-81e4-0c81cfa82bbb.png", "filename_download": "6964d750-1c00-4b9c-81e4-0c81cfa82bbb.png", "title": "E-Commerce Login Page", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.616Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.591Z", "charset": null, "filesize": "665029", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of a Simple eCommerce application login page with a clean, modern interface. The left side features a white panel with email and password input fields, sign in button, and forgot password link. The right side has a dark blue-green gradient background. The Directus logo appears in the top-left corner.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.587Z"}, {"id": "6c052e97-065c-41df-862c-aff8585dd9c0", "storage": "s3", "filename_disk": "6c052e97-065c-41df-862c-aff8585dd9c0.mp4", "filename_download": "2024_02_2b481d7a81ecc64b428d892a736d8314.mp4", "title": "2024 02 2b481d7a81ecc64b428d892a736d8314.mp4", "type": "video/mp4", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:56:05.885Z", "modified_by": null, "modified_on": "2025-07-10T14:56:09.249Z", "charset": null, "filesize": "2900137", "width": null, "height": null, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "734055a5-0440-4b19-bf2f-5459101beed1", "storage": "s3", "filename_disk": "734055a5-0440-4b19-bf2f-5459101beed1.mp4", "filename_download": "更多视频请在tg收藏夹输入@AnchorPorn (2) - Trim.mp4", "title": "更多视频请在tg收藏夹输入@anchor Porn (2)   Trim", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T01:59:19.486Z", "modified_by": null, "modified_on": "2025-07-09T01:59:42.126Z", "charset": null, "filesize": "95191723", "width": null, "height": null, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T01:59:42.123Z"}, {"id": "7775c53a-6c2c-453d-8c22-8b5445121d10", "storage": "s3", "filename_disk": "7775c53a-6c2c-453d-8c22-8b5445121d10.jpeg", "filename_download": "7775c53a-6c2c-453d-8c22-8b5445121d10.jpeg", "title": "Business Rabbit with Laptop", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.340Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.204Z", "charset": null, "filesize": "358326", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital art portrait of an anthropomorphic rabbit wearing a business suit with a navy jacket and bright pink tie, standing at a desk with a laptop. The image blends photorealistic rabbit features with human clothing and posture in an office setting.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.201Z"}, {"id": "861deca5-34ab-4d70-b01d-af7ef57ed12a", "storage": "s3", "filename_disk": "861deca5-34ab-4d70-b01d-af7ef57ed12a.jpg", "filename_download": "Camera_XHS_17438643200761040g2sg312c7qobd62005n3fo5b44d96el968fo.jpg", "title": "Camera Xhs 17438643200761040g2sg312c7qobd62005n3fo5b44d96el968fo.jpg", "type": "image/jpeg", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:40:54.431Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T14:43:18.885Z", "charset": null, "filesize": "756334", "width": 1440, "height": 2162, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "8a652e52-a275-4dde-9fc5-edf2188afe56", "storage": "s3", "filename_disk": "8a652e52-a275-4dde-9fc5-edf2188afe56.jpg", "filename_download": "8a652e52-a275-4dde-9fc5-edf2188afe56.jpg", "title": "Hero Center Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:52.840Z", "modified_by": null, "modified_on": "2025-07-09T03:48:53.098Z", "charset": null, "filesize": "80086", "width": 906, "height": 800, "duration": null, "embed": null, "description": "Marketing hero section image for Directus CMS with the tagline 'Headless but not brainless'. The image includes a brief description about Directus providing a backend for headless CMS use cases with additional features like authentication and user permissions. Below is a simplified UI mockup with purple accent colors.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:53.095Z"}, {"id": "8e7a7a37-e50b-43fe-a0b1-93b17d913dfd", "storage": "s3", "filename_disk": "8e7a7a37-e50b-43fe-a0b1-93b17d913dfd.png", "filename_download": "generation-06383666-4170-4bfc-b248-75a62ad58275-0.png", "title": "Generation 06383666 4170 4bfc B248 75a62ad58275 0.png", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:42:22.284Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T14:43:18.885Z", "charset": null, "filesize": "194552", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "8f748634-d77b-4985-b27e-7e1f3559881a", "storage": "s3", "filename_disk": "8f748634-d77b-4985-b27e-7e1f3559881a.jpeg", "filename_download": "8f748634-d77b-4985-b27e-7e1f3559881a.jpeg", "title": "Scholarly Rabbit Portrait", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.508Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.943Z", "charset": null, "filesize": "98432", "width": 512, "height": 512, "duration": null, "embed": null, "description": "Black and white illustration of an anthropomorphic white rabbit dressed in Victorian or 19th century scholar attire with a coat and cravat. The rabbit is depicted writing with a quill pen in an open book or ledger, presented in a vintage engraving style.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.940Z"}, {"id": "9538d9ef-879c-40d2-99d2-05bf731bf864", "storage": "s3", "filename_disk": "9538d9ef-879c-40d2-99d2-05bf731bf864.ico", "filename_download": "favicon(1).ico", "title": "Favicon(1)", "type": "image/x-icon", "folder": null, "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:42:19.825Z", "modified_by": null, "modified_on": "2025-07-08T05:42:20.756Z", "charset": null, "filesize": "81039", "width": null, "height": null, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:42:20.753Z"}, {"id": "9a52e835-e131-4290-81bb-5a512599f93e", "storage": "s3", "filename_disk": "9a52e835-e131-4290-81bb-5a512599f93e.png", "filename_download": "9a52e835-e131-4290-81bb-5a512599f93e.png", "title": "Generated Technical Illustration", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:49:02.692Z", "modified_by": null, "modified_on": "2025-07-09T03:49:04.745Z", "charset": null, "filesize": "1834271", "width": 1200, "height": 686, "duration": null, "embed": null, "description": "A wide-format technical or conceptual illustration, likely AI-generated based on the filename pattern. The image has landscape orientation with dimensions suitable for presentations, documentation, or web design mockups.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:49:04.742Z"}, {"id": "a051ea01-07a5-45cb-bcc6-411af9560be5", "storage": "s3", "filename_disk": "a051ea01-07a5-45cb-bcc6-411af9560be5.png", "filename_download": "a051ea01-07a5-45cb-bcc6-411af9560be5.png", "title": "Directus CRM Tags Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.114Z", "modified_by": null, "modified_on": "2025-07-09T03:48:54.427Z", "charset": null, "filesize": "297608", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus CRM tags management interface. The screen shows a list of tags including 'VIP' (blue tag) and 'Key Account' (red tag) with their associated collections (Contacts and Organizations). The left sidebar displays the CRM navigation menu and the purple Directus logo.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:54.419Z"}, {"id": "a64254ca-2b91-47f6-bbc2-30d97f0c9c0e", "storage": "s3", "filename_disk": "a64254ca-2b91-47f6-bbc2-30d97f0c9c0e.png", "filename_download": "avatar2.png", "title": "Avatar2", "type": "image/png", "folder": null, "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T00:14:40.478Z", "modified_by": null, "modified_on": "2025-07-09T00:14:42.512Z", "charset": null, "filesize": "815578", "width": 1023, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T00:14:42.510Z"}, {"id": "ac905071-0643-4337-8f53-48ed45b1ccf2", "storage": "s3", "filename_disk": "ac905071-0643-4337-8f53-48ed45b1ccf2.jpg", "filename_download": "ac905071-0643-4337-8f53-48ed45b1ccf2.jpg", "title": "Tree Silhouette Against Starry Sunset", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.162Z", "modified_by": null, "modified_on": "2025-07-09T03:48:54.468Z", "charset": null, "filesize": "300062", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Breathtaking landscape photograph of a solitary tree silhouetted against a vibrant orange and gold sunset sky filled with stars. The dark silhouette of the tree and distant hills creates a dramatic contrast against the warm sunset glow and the deep blue-black of the night sky above.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:54.462Z"}, {"id": "ae390ba1-fcff-4b99-a445-5f19257095d1", "storage": "s3", "filename_disk": "ae390ba1-fcff-4b99-a445-5f19257095d1.svg", "filename_download": "ae390ba1-fcff-4b99-a445-5f19257095d1.svg", "title": "<PERSON><PERSON>", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.281Z", "modified_by": null, "modified_on": "2025-07-09T03:48:54.491Z", "charset": null, "filesize": "11427", "width": null, "height": null, "duration": null, "embed": null, "description": "White SVG version of the Directus logo featuring the iconic gazelle silhouette. This version is optimized for use on dark backgrounds. As a vector graphic, it maintains perfect quality at any size and can be used for websites, applications, and marketing materials.", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:54.486Z"}, {"id": "b9db00d9-535f-4e24-8a46-5f7e5fc65bf2", "storage": "s3", "filename_disk": "b9db00d9-535f-4e24-8a46-5f7e5fc65bf2.jpg", "filename_download": "b9db00d9-535f-4e24-8a46-5f7e5fc65bf2.jpg", "title": "UI/UX Wireframe Sketching", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:54.378Z", "modified_by": null, "modified_on": "2025-07-09T03:48:54.701Z", "charset": null, "filesize": "129160", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Close-up photograph of a hand holding a yellow pen drawing UI wireframes on paper. The image shows three linked mobile app screen mockups with navigation arrows, interface elements, and highlighted areas in orange. The person has light-colored nail polish and appears to be sketching preliminary design concepts.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:54.699Z"}, {"id": "c0c78144-51da-4017-9820-9477c7e3c1b5", "storage": "s3", "filename_disk": "c0c78144-51da-4017-9820-9477c7e3c1b5.mp4", "filename_download": "芭蕾教师极品该有的样子潮吹吧_1463158824156774400_720x1280.mp4", "title": "芭蕾教师极品该有的样子潮吹吧 1463158824156774400 720x1280.mp4", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T07:16:37.279Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-10T07:21:46.214Z", "charset": null, "filesize": "24599771", "width": null, "height": null, "duration": null, "embed": null, "description": "nude stocking", "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "c2a301bd-74ed-4a50-9b85-3cb1f40f8dee", "storage": "s3", "filename_disk": "c2a301bd-74ed-4a50-9b85-3cb1f40f8dee.png", "filename_download": "c2a301bd-74ed-4a50-9b85-3cb1f40f8dee.png", "title": "Directus E-Commerce Product Editor", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.632Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.631Z", "charset": null, "filesize": "630837", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus e-commerce product editing interface for a 'Directus Super Soft T-Shirt'. The screen shows various product fields including title, status (Active), slug, category (Shirts), and a product description editor with humorous content about a developer t-shirt with the Directus rabbit logo.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.629Z"}, {"id": "c5de4b95-ca37-46f8-8ef3-33d6ffcec249", "storage": "s3", "filename_disk": "c5de4b95-ca37-46f8-8ef3-33d6ffcec249.jpg", "filename_download": "beileimao.jpg", "title": "be<PERSON><PERSON><PERSON>", "type": "image/jpeg", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T14:12:51.518Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T14:24:31.649Z", "charset": null, "filesize": "114545", "width": 576, "height": 1280, "duration": null, "embed": null, "description": "  girl", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T14:12:52.312Z"}, {"id": "d4fd6edc-4cc5-48c1-8bc7-e646924bbdca", "storage": "s3", "filename_disk": "d4fd6edc-4cc5-48c1-8bc7-e646924bbdca.jpeg", "filename_download": "d4fd6edc-4cc5-48c1-8bc7-e646924bbdca.jpeg", "title": "Rabbit in Blue Hoodie", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.198Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.540Z", "charset": null, "filesize": "395249", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital artwork of an anthropomorphic rabbit wearing a navy blue hoodie or jacket, standing upright with hands in pockets. The image features a realistic rabbit head with large ears and expressive eyes, combined with human posture and clothing. The background is a blurred path or tunnel creating depth and focus on the character.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.537Z"}, {"id": "d507cfb1-ee2c-46f7-a279-bf94da6c413e", "storage": "s3", "filename_disk": "d507cfb1-ee2c-46f7-a279-bf94da6c413e.png", "filename_download": "generation-ab7d4a2d-214e-418f-9600-29b708ef3378-0.png", "title": "Generation Ab7d4a2d 214e 418f 9600 29b708ef3378 0", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:46:58.879Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T05:56:52.624Z", "charset": null, "filesize": "1048989", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:47:01.214Z"}, {"id": "d5a1290f-8819-4e7c-b292-bffe5b1c8274", "storage": "s3", "filename_disk": "d5a1290f-8819-4e7c-b292-bffe5b1c8274.jpg", "filename_download": "d5a1290f-8819-4e7c-b292-bffe5b1c8274.jpg", "title": "Hero Left Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.137Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.673Z", "charset": null, "filesize": "80496", "width": 1188, "height": 663, "duration": null, "embed": null, "description": "Left-aligned website hero section for Directus CMS featuring the tagline 'Headless but not brainless' and a brief description of <PERSON>us as a backend for headless CMS use cases with authentication and user permissions. The left side shows a simplified UI mockup with purple accent colors against a light pattern background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.671Z"}, {"id": "d627d585-2c14-4bbf-89ca-34581083cc1d", "storage": "s3", "filename_disk": "d627d585-2c14-4bbf-89ca-34581083cc1d.png", "filename_download": "d627d585-2c14-4bbf-89ca-34581083cc1d.png", "title": "Webmaster Developer Avatar", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.444Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.169Z", "charset": null, "filesize": "1347150", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital avatar representing a webmaster or web developer character in portrait orientation. The image features a stylized character design suitable for profile pictures or user representations in web development, IT, or technical contexts.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.167Z"}, {"id": "dad6ac87-110c-4204-9eac-a328a02fe524", "storage": "s3", "filename_disk": "dad6ac87-110c-4204-9eac-a328a02fe524.png", "filename_download": "generation-9b83e8ae-4a72-44fc-8512-f4a074da3e49-0.png", "title": "Generation 9b83e8ae 4a72 44fc 8512 F4a074da3e49 0", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:46:58.898Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T05:56:52.624Z", "charset": null, "filesize": "1036078", "width": 768, "height": 1024, "duration": null, "embed": null, "description": "girl-hh", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:47:01.557Z"}, {"id": "dc258f02-d1a3-47f4-9f3e-2a71a0010c56", "storage": "s3", "filename_disk": "dc258f02-d1a3-47f4-9f3e-2a71a0010c56.png", "filename_download": "dc258f02-d1a3-47f4-9f3e-2a71a0010c56.png", "title": "Directus AI Image Generator Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.379Z", "modified_by": null, "modified_on": "2025-07-09T03:48:55.748Z", "charset": null, "filesize": "559420", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus CMS AI Image Generator modal dialog. The interface shows a text prompt input area with formatting tools, where users can describe the image they want to generate. The prompt describes creating a hand-drawn marker style illustration for developer blog posts. The modal also includes color selection options and a 'Create New' button.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:55.746Z"}, {"id": "df0745c2-b6e3-4b37-b64d-55a4eb0033ab", "storage": "s3", "filename_disk": "df0745c2-b6e3-4b37-b64d-55a4eb0033ab.avif", "filename_download": "df0745c2-b6e3-4b37-b64d-55a4eb0033ab.avif", "title": "Modern Web Banner - AVIF Format", "type": "image/avif", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:56.456Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.704Z", "charset": null, "filesize": "17700", "width": 1184, "height": 666, "duration": null, "embed": null, "description": "A widescreen banner image in the modern AVIF file format, which offers better compression and quality than traditional image formats. This image demonstrates the use of next-generation image formats for web optimization. AVIF provides excellent image quality at smaller file sizes, improving website performance.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.702Z"}, {"id": "ea743e20-e6e9-4be8-a949-3771cd182810", "storage": "s3", "filename_disk": "ea743e20-e6e9-4be8-a949-3771cd182810.png", "filename_download": "ea743e20-e6e9-4be8-a949-3771cd182810.png", "title": "Directus Interface <PERSON>", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:55.993Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.235Z", "charset": null, "filesize": "35267", "width": 616, "height": 424, "duration": null, "embed": null, "description": "A simplified mockup of the Directus interface showing the dashboard with a clean, minimal design. The interface features the Directus logo in the top left, navigation elements, and a content area with placeholder elements highlighted with purple accent colors. The background has a subtle grid pattern.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.233Z"}, {"id": "f342738f-a6c9-4b00-85f3-80325af932b4", "storage": "s3", "filename_disk": "f342738f-a6c9-4b00-85f3-80325af932b4.png", "filename_download": "generation-4101b1b0-b28a-4a44-b655-ea4ac0549d0f-0.png", "title": "Generation 4101b1b0 B28a 4a44 B655 Ea4ac0549d0f 0", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:46:59.090Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T14:10:15.617Z", "charset": null, "filesize": "1069309", "width": 768, "height": 1024, "duration": null, "embed": null, "description": "white pantyhose\n\nsit", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:47:01.529Z"}, {"id": "f7808f9c-79e1-43f1-9390-a7b1f4b12b83", "storage": "s3", "filename_disk": "f7808f9c-79e1-43f1-9390-a7b1f4b12b83.png", "filename_download": "generation-25881f00-0a8e-4d7a-abd1-c3fea03366d0-0.png", "title": "Generation 25881f00 0a8e 4d7a Abd1 C3fea03366d0 0", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:46:58.875Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T05:56:52.624Z", "charset": null, "filesize": "976976", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:47:00.420Z"}, {"id": "f7f00a96-228c-4acd-a53d-8ba28c9f7ea2", "storage": "s3", "filename_disk": "f7f00a96-228c-4acd-a53d-8ba28c9f7ea2.png", "filename_download": "generation-2413047d-16b7-4f13-a4a1-a71d6a2e7be8.png", "title": "Generation 2413047d 16b7 4f13 A4a1 A71d6a2e7be8.png", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-10T14:40:04.701Z", "modified_by": null, "modified_on": "2025-07-10T14:40:06.234Z", "charset": null, "filesize": "1095931", "width": 768, "height": 1024, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "f93fb826-a61d-49b7-9654-34d6d13cc8d7", "storage": "s3", "filename_disk": "f93fb826-a61d-49b7-9654-34d6d13cc8d7.png", "filename_download": "generation-34e97ac6-b098-4a49-882b-930966f190a4-0.png", "title": "Generation 34e97ac6 B098 4a49 882b 930966f190a4 0", "type": "image/png", "folder": "4cbcf356-ff5f-405e-aebb-b3db4bd100e3", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-08T05:46:58.007Z", "modified_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "modified_on": "2025-07-08T14:10:56.194Z", "charset": null, "filesize": "777950", "width": 768, "height": 1024, "duration": null, "embed": null, "description": "stand black pantyhose", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-08T05:47:00.223Z"}, {"id": "f99e3dbd-f77a-44dd-911c-50421b6aea80", "storage": "s3", "filename_disk": "f99e3dbd-f77a-44dd-911c-50421b6aea80.png", "filename_download": "favicon-removebg-preview.png", "title": "Favicon Removebg Preview.png", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T14:31:02.840Z", "modified_by": null, "modified_on": "2025-07-09T14:31:04.961Z", "charset": null, "filesize": "269514", "width": 500, "height": 500, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": null}, {"id": "fd6440c2-dd48-4792-9d08-3124cd99b40f", "storage": "s3", "filename_disk": "fd6440c2-dd48-4792-9d08-3124cd99b40f.png", "filename_download": "fd6440c2-dd48-4792-9d08-3124cd99b40f.png", "title": "Digital Concept Art - Widescreen Format", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:59.426Z", "modified_by": null, "modified_on": "2025-07-09T03:49:03.076Z", "charset": null, "filesize": "1600543", "width": 1200, "height": 686, "duration": null, "embed": null, "description": "High-resolution widescreen digital artwork in landscape orientation (1792×1024 pixels). This versatile image is optimized for website hero sections, presentation backgrounds, or digital marketing materials. The wide aspect ratio provides ample space for visual storytelling and graphic elements while maintaining excellent quality for various display sizes.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:49:03.073Z"}, {"id": "fd884e36-0da2-41a0-9b27-881bb643a161", "storage": "s3", "filename_disk": "fd884e36-0da2-41a0-9b27-881bb643a161.mp4", "filename_download": "guochan2048.com -2020_08_12_01_59_IMG_1770.mp4", "title": "Guochan2048.com  2020 08 12 01 59 Img 1770", "type": "video/mp4", "folder": "4e9f3b03-e936-40a9-aeaa-a3f123139ec4", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T02:01:11.390Z", "modified_by": null, "modified_on": "2025-07-09T02:01:24.117Z", "charset": null, "filesize": "23531330", "width": null, "height": null, "duration": null, "embed": null, "description": null, "location": null, "tags": null, "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T02:01:24.114Z"}, {"id": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1", "storage": "s3", "filename_disk": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1.jpg", "filename_download": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1.jpg", "title": "Hero Right Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "created_on": "2025-07-09T03:48:56.204Z", "modified_by": null, "modified_on": "2025-07-09T03:48:56.455Z", "charset": null, "filesize": "78328", "width": 1229, "height": 663, "duration": null, "embed": null, "description": "Right-aligned website hero section for Directus CMS featuring the tagline 'Headless but not brainless' and a brief description about Directus providing backend solutions for headless CMS use cases. The right side displays a simplified UI mockup with purple accent colors, while the left contains marketing text against a clean background.", "location": null, "tags": null, "metadata": {}, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-07-09T03:48:56.448Z"}]