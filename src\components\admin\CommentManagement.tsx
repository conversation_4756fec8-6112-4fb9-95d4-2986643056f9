'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  MessageSquare, 
  MoreHorizontal, 
  Check, 
  X, 
  Trash2, 
  Search,
  Eye,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { Comment } from '@/types/directus-schema';
import { toast } from '@/lib/toast';

interface CommentManagementProps {
  onCommentUpdate?: () => void;
}

export default function CommentManagement({ onCommentUpdate }: CommentManagementProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'spam'>('all');
  const [selectedComments, setSelectedComments] = useState<string[]>([]);

  // 获取评论列表
  const fetchComments = async () => {
    try {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/admin/comments?${params}`);
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      } else {
        throw new Error('Failed to fetch comments');
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.error('获取评论列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新评论状态
  const updateCommentStatus = async (commentId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        setComments(comments.map(comment => 
          comment.id === commentId ? { ...comment, status } : comment
        ));
        toast.success(`评论已${getStatusText(status)}`);
        if (onCommentUpdate) onCommentUpdate();
      } else {
        throw new Error('Failed to update comment');
      }
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('更新评论状态失败');
    }
  };

  // 删除评论
  const deleteComment = async (commentId: string) => {
    if (!confirm('确定要删除这条评论吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/comments/${commentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setComments(comments.filter(comment => comment.id !== commentId));
        toast.success('评论删除成功');
        if (onCommentUpdate) onCommentUpdate();
      } else {
        throw new Error('Failed to delete comment');
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('删除评论失败');
    }
  };

  // 批量更新评论状态
  const batchUpdateStatus = async (status: string) => {
    if (selectedComments.length === 0) {
      toast.error('请先选择要操作的评论');
      return;
    }

    try {
      const response = await fetch('/api/admin/comments', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          commentIds: selectedComments,
          status,
        }),
      });

      if (response.ok) {
        setComments(comments.map(comment => 
          selectedComments.includes(comment.id) ? { ...comment, status } : comment
        ));
        setSelectedComments([]);
        toast.success(`${selectedComments.length} 条评论已${getStatusText(status)}`);
        if (onCommentUpdate) onCommentUpdate();
      } else {
        throw new Error('Failed to batch update comments');
      }
    } catch (error) {
      console.error('Error batch updating comments:', error);
      toast.error('批量更新失败');
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return '通过审核';
      case 'rejected': return '拒绝';
      case 'spam': return '标记为垃圾评论';
      default: return '更新';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">已审核</Badge>;
      case 'pending':
        return <Badge variant="secondary">待审核</Badge>;
      case 'rejected':
        return <Badge variant="destructive">已拒绝</Badge>;
      case 'spam':
        return <Badge variant="outline" className="border-red-300 text-red-800">垃圾评论</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 过滤评论
  const filteredComments = comments.filter(comment => {
    const matchesSearch = comment.author_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         comment.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || comment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // 统计数据
  const stats = {
    total: comments.length,
    pending: comments.filter(c => c.status === 'pending').length,
    approved: comments.filter(c => c.status === 'approved').length,
    rejected: comments.filter(c => c.status === 'rejected').length,
    spam: comments.filter(c => c.status === 'spam').length,
  };

  useEffect(() => {
    fetchComments();
  }, [statusFilter]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">评论管理</h1>
          <p className="text-gray-600">管理和审核用户评论</p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">总评论</p>
                <p className="text-xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">待审核</p>
                <p className="text-xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Check className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">已通过</p>
                <p className="text-xl font-bold">{stats.approved}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <X className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">已拒绝</p>
                <p className="text-xl font-bold">{stats.rejected}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">垃圾评论</p>
                <p className="text-xl font-bold">{stats.spam}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索评论..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            {['all', 'pending', 'approved', 'rejected', 'spam'].map((status) => (
              <Button
                key={status}
                variant={statusFilter === status ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFilter(status as any)}
              >
                {status === 'all' ? '全部' : 
                 status === 'pending' ? '待审核' :
                 status === 'approved' ? '已通过' :
                 status === 'rejected' ? '已拒绝' : '垃圾评论'}
              </Button>
            ))}
          </div>
        </div>

        {/* 批量操作 */}
        {selectedComments.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              已选择 {selectedComments.length} 条
            </span>
            <Button size="sm" onClick={() => batchUpdateStatus('approved')}>
              批量通过
            </Button>
            <Button size="sm" variant="outline" onClick={() => batchUpdateStatus('rejected')}>
              批量拒绝
            </Button>
            <Button size="sm" variant="destructive" onClick={() => batchUpdateStatus('spam')}>
              标记垃圾
            </Button>
          </div>
        )}
      </div>

      {/* 评论列表 */}
      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <input
                  type="checkbox"
                  checked={selectedComments.length === filteredComments.length && filteredComments.length > 0}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedComments(filteredComments.map(c => c.id));
                    } else {
                      setSelectedComments([]);
                    }
                  }}
                />
              </TableHead>
              <TableHead>作者</TableHead>
              <TableHead>内容</TableHead>
              <TableHead>文章</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>时间</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredComments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                  {searchTerm ? '没有找到匹配的评论' : '还没有评论'}
                </TableCell>
              </TableRow>
            ) : (
              filteredComments.map((comment) => (
                <TableRow key={comment.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedComments.includes(comment.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedComments([...selectedComments, comment.id]);
                        } else {
                          setSelectedComments(selectedComments.filter(id => id !== comment.id));
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{comment.author_name}</div>
                      <div className="text-sm text-gray-500">{comment.author_email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <p className="text-sm line-clamp-2">{comment.content}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    {typeof comment.post === 'object' && comment.post ? (
                      <div>
                        <div className="font-medium text-sm">{comment.post.title}</div>
                        <div className="text-xs text-gray-500">/blog/{comment.post.slug}</div>
                      </div>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(comment.status || 'pending')}
                  </TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {formatDate(comment.date_created)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => updateCommentStatus(comment.id, 'approved')}>
                          <Check className="h-4 w-4 mr-2" />
                          通过
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateCommentStatus(comment.id, 'rejected')}>
                          <X className="h-4 w-4 mr-2" />
                          拒绝
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateCommentStatus(comment.id, 'spam')}>
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          标记垃圾
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => deleteComment(comment.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
