import { NextRequest, NextResponse } from 'next/server';
import { useDirectus } from '@/lib/directus/directus';
import { readItems, updateItem, deleteItem, withToken } from '@directus/sdk';

// 获取所有评论（管理员）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const postId = searchParams.get('post');

    const { directus } = useDirectus();
    const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;

    if (!adminToken) {
      return NextResponse.json(
        { error: 'Admin token not configured' },
        { status: 500 }
      );
    }

    // 构建过滤条件
    const filter: any = {};
    if (status) {
      filter.status = { _eq: status };
    }
    if (postId) {
      filter.post = { _eq: postId };
    }

    const comments = await directus.request(
      withToken(adminToken,
        readItems('comments', {
          filter: Object.keys(filter).length > 0 ? filter : undefined,
          fields: [
            'id',
            'content',
            'author_name',
            'author_email',
            'author_website',
            'author_ip',
            'status',
            'date_created',
            'date_updated',
            { post: ['id', 'title', 'slug'] },
            { parent: ['id', 'author_name'] }
          ],
          sort: ['-date_created'],
          limit: -1
        })
      )
    );

    return NextResponse.json(comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

// 批量更新评论状态
export async function PATCH(request: NextRequest) {
  try {
    const { commentIds, status } = await request.json();
    const { directus } = useDirectus();
    const adminToken = process.env.DIRECTUS_ADMIN_TOKEN;

    if (!adminToken) {
      return NextResponse.json(
        { error: 'Admin token not configured' },
        { status: 500 }
      );
    }

    if (!commentIds || !Array.isArray(commentIds) || !status) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // 批量更新评论状态
    const updatePromises = commentIds.map(id =>
      directus.request(
        withToken(adminToken,
          updateItem('comments', id, { status })
        )
      )
    );

    await Promise.all(updatePromises);

    return NextResponse.json({ 
      success: true, 
      message: `${commentIds.length} 条评论状态已更新为 ${status}` 
    });
  } catch (error) {
    console.error('Error updating comments:', error);
    return NextResponse.json(
      { error: 'Failed to update comments' },
      { status: 500 }
    );
  }
}
