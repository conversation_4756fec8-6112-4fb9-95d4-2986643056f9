/**
 * 评论系统安全工具
 * 提供评论内容过滤、验证和安全检查功能
 */

// 敏感词过滤列表（示例）
const SPAM_KEYWORDS = [
  '广告', '推广', '加微信', '赚钱', '投资', '贷款',
  'spam', 'advertisement', 'promotion', 'casino'
];

// 恶意链接模式
const MALICIOUS_URL_PATTERNS = [
  /bit\.ly/i,
  /tinyurl/i,
  /短链/i,
  // 可以添加更多可疑域名模式
];

/**
 * 验证评论内容
 */
export function validateCommentContent(content: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 检查内容长度
  if (!content || content.trim().length === 0) {
    errors.push('评论内容不能为空');
  } else if (content.length > 2000) {
    errors.push('评论内容不能超过2000个字符');
  } else if (content.length < 2) {
    errors.push('评论内容至少需要2个字符');
  }

  // 检查是否包含过多链接
  const urlCount = (content.match(/https?:\/\/[^\s]+/g) || []).length;
  if (urlCount > 2) {
    errors.push('评论中链接数量过多');
  }

  // 检查重复字符
  const repeatedChars = /(.)\1{10,}/g;
  if (repeatedChars.test(content)) {
    errors.push('评论包含过多重复字符');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证作者信息
 */
export function validateAuthorInfo(authorName: string, authorEmail: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证姓名
  if (!authorName || authorName.trim().length === 0) {
    errors.push('姓名不能为空');
  } else if (authorName.length > 50) {
    errors.push('姓名不能超过50个字符');
  } else if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(authorName)) {
    errors.push('姓名只能包含中文、英文和空格');
  }

  // 验证邮箱
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!authorEmail || authorEmail.trim().length === 0) {
    errors.push('邮箱不能为空');
  } else if (!emailRegex.test(authorEmail)) {
    errors.push('邮箱格式不正确');
  } else if (authorEmail.length > 100) {
    errors.push('邮箱不能超过100个字符');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 检查是否为垃圾评论
 */
export function checkSpamContent(content: string, authorName: string, authorEmail: string): {
  isSpam: boolean;
  reasons: string[];
} {
  const reasons: string[] = [];

  // 检查敏感词
  const lowerContent = content.toLowerCase();
  const foundKeywords = SPAM_KEYWORDS.filter(keyword => 
    lowerContent.includes(keyword.toLowerCase())
  );
  
  if (foundKeywords.length > 0) {
    reasons.push(`包含敏感词: ${foundKeywords.join(', ')}`);
  }

  // 检查恶意链接
  const hasmaliciousUrl = MALICIOUS_URL_PATTERNS.some(pattern => 
    pattern.test(content)
  );
  
  if (hasmaliciousUrl) {
    reasons.push('包含可疑链接');
  }

  // 检查全大写内容（可能是垃圾信息）
  if (content.length > 20 && content === content.toUpperCase()) {
    reasons.push('内容全部为大写字母');
  }

  // 检查邮箱域名
  const suspiciousDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com'];
  const emailDomain = authorEmail.split('@')[1]?.toLowerCase();
  if (emailDomain && suspiciousDomains.includes(emailDomain)) {
    reasons.push('使用临时邮箱');
  }

  // 检查姓名是否为随机字符
  if (authorName.length > 10 && !/[\u4e00-\u9fa5]/.test(authorName) && 
      !/\s/.test(authorName) && /^[a-zA-Z]+$/.test(authorName)) {
    const vowels = (authorName.match(/[aeiou]/gi) || []).length;
    const consonants = authorName.length - vowels;
    if (vowels === 0 || consonants / vowels > 4) {
      reasons.push('姓名可能为随机字符');
    }
  }

  return {
    isSpam: reasons.length > 0,
    reasons
  };
}

/**
 * 清理和转义评论内容
 */
export function sanitizeCommentContent(content: string): string {
  // 移除HTML标签（基础版本）
  let cleaned = content.replace(/<[^>]*>/g, '');
  
  // 转义特殊字符
  cleaned = cleaned
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');

  // 移除多余的空白字符
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
}

/**
 * 生成评论摘要（用于管理后台显示）
 */
export function generateCommentSummary(content: string, maxLength: number = 100): string {
  const cleaned = sanitizeCommentContent(content);
  
  if (cleaned.length <= maxLength) {
    return cleaned;
  }
  
  return cleaned.substring(0, maxLength - 3) + '...';
}

/**
 * 检查评论频率限制
 */
export function checkRateLimit(ip: string, timeWindow: number = 300000): { // 5分钟
  allowed: boolean;
  remainingTime?: number;
} {
  // 这里应该使用 Redis 或其他缓存系统来实现
  // 目前返回简单的允许状态
  // 在生产环境中，你需要实现真正的频率限制逻辑
  
  return { allowed: true };
}

/**
 * 验证网站URL格式
 */
export function validateWebsiteUrl(url: string): {
  isValid: boolean;
  error?: string;
} {
  if (!url || url.trim().length === 0) {
    return { isValid: true }; // 网站URL是可选的
  }

  try {
    const urlObj = new URL(url);
    
    // 只允许 http 和 https 协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: '只支持 HTTP 和 HTTPS 协议' };
    }

    // 检查域名长度
    if (urlObj.hostname.length > 100) {
      return { isValid: false, error: '域名过长' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'URL 格式不正确' };
  }
}

/**
 * 综合评论验证
 */
export function validateComment(data: {
  content: string;
  author_name: string;
  author_email: string;
  author_website?: string;
  author_ip?: string;
}): {
  isValid: boolean;
  errors: string[];
  isSpam: boolean;
  spamReasons: string[];
} {
  const contentValidation = validateCommentContent(data.content);
  const authorValidation = validateAuthorInfo(data.author_name, data.author_email);
  const websiteValidation = data.author_website ? 
    validateWebsiteUrl(data.author_website) : { isValid: true };
  const spamCheck = checkSpamContent(data.content, data.author_name, data.author_email);

  const allErrors = [
    ...contentValidation.errors,
    ...authorValidation.errors,
    ...(websiteValidation.error ? [websiteValidation.error] : [])
  ];

  return {
    isValid: allErrors.length === 0 && websiteValidation.isValid,
    errors: allErrors,
    isSpam: spamCheck.isSpam,
    spamReasons: spamCheck.reasons
  };
}
