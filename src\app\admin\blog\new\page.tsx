'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import BlogEditor from '@/components/admin/BlogEditor';
import { AuthGuard } from '@/contexts/AuthContext';
import { Post } from '@/types/directus-schema';
import { toast } from '@/lib/toast';

export default function NewBlogPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async (postData: Partial<Post>) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/admin/blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      });

      if (response.ok) {
        const newPost = await response.json();
        toast.success(
          postData.status === 'published'
            ? '博客发布成功！'
            : '博客草稿保存成功！'
        );

        // 跳转到编辑页面或博客列表
        if (postData.status === 'published') {
          router.push(`/blog/${newPost.slug}`);
        } else {
          router.push(`/admin/blog/edit/${newPost.id}`);
        }
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save post');
      }
    } catch (error) {
      console.error('Error saving post:', error);
      toast.error('保存博客失败：' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreview = (postData: Partial<Post>) => {
    // 在新窗口中打开预览
    // 这里可以实现预览功能，比如打开一个预览模态框
    console.log('Preview post:', postData);
    toast.info('预览功能开发中...');
  };

  return (
    <AuthGuard>
      <AdminLayout>
        <BlogEditor
          onSave={handleSave}
          onPreview={handlePreview}
          isLoading={isLoading}
        />
      </AdminLayout>
    </AuthGuard>
  );
}
