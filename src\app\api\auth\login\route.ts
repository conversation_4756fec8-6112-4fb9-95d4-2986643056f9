import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const { email, password } = await request.json();

		// 验证输入
		if (!email || !password) {
			return NextResponse.json({ error: '邮箱和密码不能为空' }, { status: 400 });
		}

		// 直接使用 Directus REST API 进行身份验证
		const authResponse = await fetch(`${process.env.NEXT_PUBLIC_DIRECTUS_URL}/auth/login`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				email,
				password,
			}),
		});

		if (!authResponse.ok) {
			const errorData = await authResponse.json().catch(() => ({}));
			return NextResponse.json({ error: errorData.errors?.[0]?.message || '邮箱或密码错误' }, { status: 401 });
		}

		const authResult = await authResponse.json();

		if (!authResult.data?.access_token) {
			return NextResponse.json({ error: '登录失败，请检查邮箱和密码' }, { status: 401 });
		}

		// 获取用户信息
		const userResponse = await fetch(`${process.env.NEXT_PUBLIC_DIRECTUS_URL}/users/me`, {
			headers: {
				Authorization: `Bearer ${authResult.data.access_token}`,
			},
		});

		if (!userResponse.ok) {
			return NextResponse.json({ error: '获取用户信息失败' }, { status: 500 });
		}

		const userData = await userResponse.json();

		// 检查用户是否有管理员权限
		if (!userData.data.role || userData.data.role.admin_access !== true) {
			return NextResponse.json({ error: '您没有管理员权限' }, { status: 403 });
		}

		// 返回认证信息
		return NextResponse.json({
			token: authResult.data.access_token,
			refresh_token: authResult.data.refresh_token,
			expires: authResult.data.expires,
			user: {
				id: userData.data.id,
				email: userData.data.email,
				first_name: userData.data.first_name,
				last_name: userData.data.last_name,
				role: userData.data.role,
			},
		});
	} catch (error) {
		console.error('Login error:', error);

		// 处理不同类型的错误
		if (error instanceof Error) {
			if (error.message.includes('Invalid user credentials')) {
				return NextResponse.json({ error: '邮箱或密码错误' }, { status: 401 });
			}
		}

		return NextResponse.json({ error: '登录失败，请稍后重试' }, { status: 500 });
	}
}
