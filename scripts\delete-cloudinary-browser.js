/**
 * 浏览器版本 - Cloudinary 文件删除脚本
 *
 * 此脚本可以在 Cloudinary 控制台的浏览器开发者工具中运行
 *
 * 使用方法:
 * 1. 登录 Cloudinary 控制台 (https://cloudinary.com/console)
 * 2. 打开浏览器开发者工具 (F12)
 * 3. 切换到 Console 标签页
 * 4. 复制并粘贴此脚本代码
 * 5. 按 Enter 执行
 */

(async function deleteAllCloudinaryFiles() {
  console.log('🚀 开始删除 Cloudinary 文件...');

  // 从当前页面获取 Cloudinary 配置
  const cloudName = window.location.hostname.includes('cloudinary.com') ?
    window.location.pathname.split('/')[2] : null;

  if (!cloudName) {
    console.error('❌ 请在 Cloudinary 控制台页面运行此脚本');
    return;
  }

  console.log(`🔗 云名称: ${cloudName}`);

  // 安全确认
  const confirmed = confirm(
    '⚠️ 警告: 此操作将删除 Cloudinary 中的所有文件！\n' +
    '⚠️ 此操作不可逆转！\n\n' +
    '确定要继续吗？'
  );

  if (!confirmed) {
    console.log('❌ 操作已取消');
    return;
  }

  try {
    let totalDeleted = 0;
    let hasMore = true;
    let nextCursor = null;

    // 获取 API 令牌 (需要从页面中提取)
    const apiKey = prompt('请输入您的 API Key:');
    const apiSecret = prompt('请输入您的 API Secret:');

    if (!apiKey || !apiSecret) {
      console.error('❌ 需要 API Key 和 API Secret');
      return;
    }

    // 生成 API 签名的简化版本
    const generateSignature = (params, secret) => {
      // 注意: 这是一个简化的签名生成，实际生产中需要使用更安全的方法
      const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&');
      return btoa(sortedParams + secret).replace(/[^a-zA-Z0-9]/g, '').substring(0, 40);
    };

    while (hasMore) {
      console.log('📡 获取文件列表...');

      // 构建 API 请求参数
      const timestamp = Math.floor(Date.now() / 1000);
      const params = {
        api_key: apiKey,
        timestamp: timestamp,
        max_results: 500
      };

      if (nextCursor) {
        params.next_cursor = nextCursor;
      }

      // 生成签名
      params.signature = generateSignature(params, apiSecret);

      // 构建请求 URL
      const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
      const url = `https://api.cloudinary.com/v1_1/${cloudName}/resources/image?${queryString}`;

      try {
        const response = await fetch(url);
        const data = await response.json();

        if (!response.ok) {
          console.error('❌ API 请求失败:', data.error?.message || 'Unknown error');
          break;
        }

        if (data.resources && data.resources.length > 0) {
          console.log(`📁 找到 ${data.resources.length} 个文件，准备删除...`);

          // 批量删除文件
          const publicIds = data.resources.map(resource => resource.public_id);

          for (let i = 0; i < publicIds.length; i += 100) {
            const batch = publicIds.slice(i, i + 100);

            const deleteParams = {
              api_key: apiKey,
              timestamp: Math.floor(Date.now() / 1000),
              public_ids: batch
            };

            deleteParams.signature = generateSignature(deleteParams, apiSecret);

            const deleteResponse = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(deleteParams)
            });

            if (deleteResponse.ok) {
              totalDeleted += batch.length;
              console.log(`✅ 删除了 ${batch.length} 个文件 (总计: ${totalDeleted})`);
            } else {
              console.error(`❌ 删除批次失败`);
            }

            // 添加延迟避免 API 限制
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        hasMore = data.next_cursor ? true : false;
        nextCursor = data.next_cursor;

        if (hasMore) {
          console.log('🔄 继续获取更多文件...');
        }

      } catch (error) {
        console.error('❌ 请求失败:', error.message);
        break;
      }
    }

    console.log(`\n🎉 删除完成！总计删除了 ${totalDeleted} 个文件`);
    console.log('💡 注意: 此脚本只删除了图片文件，视频文件需要单独处理');

  } catch (error) {
    console.error('❌ 删除过程中发生错误:', error.message);
  }
})();

console.log('📝 提示: 复制上面的代码并在 Cloudinary 控制台页面的开发者工具中运行');
