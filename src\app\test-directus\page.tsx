import { useDirectus } from '@/lib/directus/directus';

export default async function TestDirectusPage() {
	const { directus, readItems } = useDirectus();
	
	try {
		console.log('Testing Directus connection...');
		
		// Test basic connection
		const pages = await directus.request(
			readItems('pages', {
				limit: 1,
				fields: ['id', 'title', 'permalink']
			})
		);
		
		console.log('Pages data:', pages);
		
		// Test specific page query
		const homePageData = await directus.request(
			readItems('pages', {
				filter: { permalink: { _eq: '/' } },
				limit: 1,
				fields: [
					'title',
					'seo',
					'id',
					{
						blocks: [
							'id',
							'background',
							'collection',
							'item',
							'sort',
							'hide_block'
						]
					}
				]
			})
		);
		
		console.log('Home page data:', homePageData);
		
		return (
			<div className="p-8">
				<h1 className="text-2xl font-bold mb-4">Directus Connection Test</h1>
				<div className="space-y-4">
					<div>
						<h2 className="text-lg font-semibold">All Pages:</h2>
						<pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
							{JSON.stringify(pages, null, 2)}
						</pre>
					</div>
					<div>
						<h2 className="text-lg font-semibold">Home Page Data:</h2>
						<pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
							{JSON.stringify(homePageData, null, 2)}
						</pre>
					</div>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Directus test error:', error);
		return (
			<div className="p-8">
				<h1 className="text-2xl font-bold mb-4 text-red-600">Directus Connection Error</h1>
				<pre className="bg-red-100 p-4 rounded text-sm">
					{error instanceof Error ? error.message : String(error)}
				</pre>
			</div>
		);
	}
}
