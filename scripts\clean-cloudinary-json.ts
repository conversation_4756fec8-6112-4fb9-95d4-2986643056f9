#!/usr/bin/env tsx

/**
 * 删除 JSON 文件中所有使用 Cloudinary 存储的文件记录
 *
 * 使用方法:
 * npm run clean-cloudinary-json
 */

import fs from 'fs/promises';
import path from 'path';

interface FileRecord {
  id: string;
  storage: string;
  [key: string]: any;
}

async function removeCloudinaryFiles(): Promise<void> {
  const filePath = path.join(process.cwd(), 'templates/mycms/src/files.json');

  try {
    console.log('🔍 读取文件:', filePath);

    // 读取 JSON 文件
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const files: FileRecord[] = JSON.parse(fileContent);

    console.log(`📊 原始文件总数: ${files.length}`);

    // 统计 Cloudinary 文件
    const cloudinaryFiles = files.filter(file => file.storage === 'cloudinary');
    console.log(`☁️  Cloudinary 文件数量: ${cloudinaryFiles.length}`);

    if (cloudinaryFiles.length === 0) {
      console.log('✅ 没有找到 Cloudinary 文件，无需处理');

      return;
    }

    // 显示即将删除的文件信息
    console.log('\n📋 即将删除的 Cloudinary 文件:');
    cloudinaryFiles.slice(0, 5).forEach((file, index) => {
      console.log(`   ${index + 1}. ${file.filename_download || file.title || file.id}`);
    });

    if (cloudinaryFiles.length > 5) {
      console.log(`   ... 还有 ${cloudinaryFiles.length - 5} 个文件`);
    }

    // 确认删除
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const confirmed = await new Promise<boolean>((resolve) => {
      rl.question('\n⚠️  确定要删除这些 Cloudinary 文件记录吗？输入 "YES" 确认: ', (answer) => {
        rl.close();
        resolve(answer === 'YES');
      });
    });

    if (!confirmed) {
      console.log('❌ 操作已取消');

      return;
    }

    // 过滤掉 Cloudinary 文件
    const filteredFiles = files.filter(file => file.storage !== 'cloudinary');

    console.log('\n🔄 正在处理...');

    // 创建备份
    const backupPath = filePath + '.backup.' + Date.now();
    await fs.copyFile(filePath, backupPath);
    console.log(`💾 已创建备份: ${path.basename(backupPath)}`);

    // 写入过滤后的数据
    const newContent = JSON.stringify(filteredFiles, null, 2);
    await fs.writeFile(filePath, newContent, 'utf-8');

    console.log('\n✅ 删除完成！');
    console.log(`📊 删除前: ${files.length} 个文件`);
    console.log(`📊 删除后: ${filteredFiles.length} 个文件`);
    console.log(`🗑️  已删除: ${cloudinaryFiles.length} 个 Cloudinary 文件记录`);

    // 显示文件大小变化
    const originalSize = fileContent.length;
    const newSize = newContent.length;
    const savedBytes = originalSize - newSize;
    const savedMB = (savedBytes / 1024 / 1024).toFixed(2);

    console.log(`💾 文件大小减少: ${savedMB} MB`);

  } catch (error: any) {
    console.error('❌ 处理失败:', error.message);

    if (error.code === 'ENOENT') {
      console.error('💡 请确保文件路径正确:', filePath);
    } else if (error.name === 'SyntaxError') {
      console.error('💡 JSON 文件格式错误，请检查文件内容');
    }

    process.exit(1);
  }
}

async function main(): Promise<void> {
  try {
    console.log('🚀 开始清理 Cloudinary 文件记录...\n');
    await removeCloudinaryFiles();
    console.log('\n🎉 清理完成！');
  } catch (error: any) {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  main();
}

export { removeCloudinaryFiles };
