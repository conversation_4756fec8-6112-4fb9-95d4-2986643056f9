[{"id": "6e77d80b-5f73-414a-b930-81bd0b335dfc", "first_name": "Admin", "last_name": "User", "email": "<EMAIL>", "password": "**********", "location": null, "title": null, "description": null, "tags": null, "avatar": "a64254ca-2b91-47f6-bbc2-30d97f0c9c0e", "language": null, "tfa_secret": null, "status": "active", "role": "11a350de-444e-42e9-8c20-b12b6e5d4aea", "token": "**********", "last_access": "2025-07-12T15:31:33.810Z", "last_page": "/users", "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "87392b0c-3f7a-4760-8d7f-57931dc38b16", "first_name": null, "last_name": null, "email": "<EMAIL>", "password": "**********", "location": null, "title": null, "description": null, "tags": null, "avatar": null, "language": null, "tfa_secret": null, "status": "active", "role": "73171c5e-1f2d-40fc-951e-d2b3cba36eef", "token": null, "last_access": "2025-07-10T02:25:42.906Z", "last_page": "/content", "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "88a6e8cf-f0f8-41db-a3a2-8a9741c086cc", "first_name": "Frontend", "last_name": "Bot", "email": null, "password": null, "location": null, "title": "For server-to-server communication", "description": "This user has API only access and is meant for communicating securely with Directus from your frontend.\n\nThis user has elevated permissions over the Public to:\n- Submit Forms \n- Upload Files \n\nFrom a security perspective, you would typically not want to allow public access to upload files or submit forms.\n\n- Generate a static token access below and include it in API calls to Directus when submitting forms or uploading files from a form.\n- Be careful to only use the token server side to prevent the static access token from being exposed to the client side.", "tags": ["API"], "avatar": "14594872-a805-4251-8dfd-b93bb2effbc0", "language": null, "tfa_secret": null, "status": "active", "role": null, "token": null, "last_access": null, "last_page": null, "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "9a105323-5eec-48d4-8a79-4681fdc94276", "first_name": "Content", "last_name": "Writer", "email": "<EMAIL>", "password": null, "location": null, "title": null, "description": null, "tags": null, "avatar": "5f35b7e3-0357-47c3-807f-f132cca95e3f", "language": null, "tfa_secret": null, "status": "active", "role": "3a4464fb-2189-4710-a164-2503eed88ae7", "token": null, "last_access": "2025-04-02T19:38:38.817Z", "last_page": null, "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "first_name": "Webmaster", "last_name": null, "email": "<EMAIL>", "password": null, "location": null, "title": null, "description": null, "tags": null, "avatar": "d627d585-2c14-4bbf-89ca-34581083cc1d", "language": null, "tfa_secret": null, "status": "active", "role": "11a350de-444e-42e9-8c20-b12b6e5d4aea", "token": null, "last_access": "2025-05-07T19:37:40.249Z", "last_page": null, "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}]