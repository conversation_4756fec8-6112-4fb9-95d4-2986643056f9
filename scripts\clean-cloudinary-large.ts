#!/usr/bin/env tsx

/**
 * 快速删除 JSON 文件中的 Cloudinary 记录 (流式处理版本)
 * 适用于超大 JSON 文件
 */

import fs from 'fs';
import readline from 'readline';

async function processLargeJsonFile(): Promise<void> {
  const inputFile = 'templates/mycms/src/files.json';
  const outputFile = 'templates/mycms/src/files.cleaned.json';
  const backupFile = `templates/mycms/src/files.json.backup.${Date.now()}`;

  try {
    console.log('🔍 开始处理大型 JSON 文件...');

    // 创建备份
    console.log('💾 创建备份...');
    fs.copyFileSync(inputFile, backupFile);

    const fileStream = fs.createReadStream(inputFile);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    const writeStream = fs.createWriteStream(outputFile);
    let isFirstItem = true;
    let currentObject = '';
    let braceCount = 0;
    let inObject = false;
    let totalProcessed = 0;
    let cloudinaryCount = 0;
    let keptCount = 0;

    // 写入数组开始
    writeStream.write('[\n');

    for await (const line of rl) {
      // 如果是数组开始，跳过
      if (line.trim() === '[') continue;

      // 如果是数组结束，处理最后一个对象
      if (line.trim() === ']') {
        if (currentObject) {
          await processCurrentObject();
        }
        break;
      }

      // 累积当前对象的内容
      currentObject += line + '\n';

      // 计算大括号数量来判断对象是否完整
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          inObject = true;
        } else if (char === '}') {
          braceCount--;
        }
      }

      // 如果对象完整（大括号平衡）
      if (inObject && braceCount === 0) {
        await processCurrentObject();
        currentObject = '';
        inObject = false;
        totalProcessed++;

        if (totalProcessed % 1000 === 0) {
          console.log(`📊 已处理 ${totalProcessed} 个文件记录...`);
        }
      }
    }

    // 写入数组结束
    writeStream.write('\n]');
    writeStream.end();

    // 等待写入完成
    await new Promise<void>((resolve) => writeStream.on('finish', () => resolve()));

    // 替换原文件
    fs.renameSync(outputFile, inputFile);

    console.log('\n✅ 处理完成！');
    console.log(`📊 总计处理: ${totalProcessed} 个文件记录`);
    console.log(`☁️  删除 Cloudinary 文件: ${cloudinaryCount} 个`);
    console.log(`💾 保留文件: ${keptCount} 个`);
    console.log(`🗂️  备份文件: ${backupFile}`);

    async function processCurrentObject(): Promise<void> {
      try {
        // 移除尾部逗号和空白
        const cleanObject = currentObject.replace(/,\s*$/, '').trim();

        if (cleanObject) {
          const obj = JSON.parse(cleanObject);

          if (obj.storage !== 'cloudinary') {
            // 不是 Cloudinary 文件，保留
            if (!isFirstItem) {
              writeStream.write(',\n');
            }
            writeStream.write('  ' + JSON.stringify(obj, null, 2).split('\n').join('\n  '));
            isFirstItem = false;
            keptCount++;
          } else {
            // 是 Cloudinary 文件，跳过
            cloudinaryCount++;
          }
        }
      } catch (error) {
        console.warn('⚠️  解析对象失败，跳过:', error);
      }
    }

  } catch (error: any) {
    console.error('❌ 处理失败:', error.message);
    process.exit(1);
  }
}

if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  processLargeJsonFile();
}

export { processLargeJsonFile };
