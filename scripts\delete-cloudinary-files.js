import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

// Cloudinary 配置
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

async function deleteAllCloudinaryFiles() {
  try {
    console.log('🚀 开始删除 Cloudinary 文件...');

    let hasMore = true;
    let nextCursor = null;
    let totalDeleted = 0;

    while (hasMore) {
      // 获取资源列表
      const options = {
        max_results: 500, // 每次最多获取500个文件
        resource_type: 'auto' // 包括图片、视频等所有类型
      };

      if (nextCursor) {
        options.next_cursor = nextCursor;
      }

      const result = await cloudinary.api.resources(options);

      if (result.resources && result.resources.length > 0) {
        console.log(`📁 找到 ${result.resources.length} 个文件，准备删除...`);

        // 批量删除文件
        const publicIds = result.resources.map(resource => resource.public_id);

        try {
          const deleteResult = await cloudinary.api.delete_resources(publicIds);

          // 统计删除结果
          const deleted = Object.keys(deleteResult.deleted).filter(
            key => deleteResult.deleted[key] === 'deleted'
          ).length;

          totalDeleted += deleted;
          console.log(`✅ 成功删除 ${deleted} 个文件`);

          // 显示删除失败的文件
          const failed = Object.keys(deleteResult.deleted).filter(
            key => deleteResult.deleted[key] !== 'deleted'
          );

          if (failed.length > 0) {
            console.log(`❌ 删除失败的文件: ${failed.length} 个`);
            failed.forEach(id => {
              console.log(`   - ${id}: ${deleteResult.deleted[id]}`);
            });
          }

        } catch (deleteError) {
          console.error('❌ 批量删除失败:', deleteError.message);

          // 如果批量删除失败，尝试逐个删除
          console.log('🔄 尝试逐个删除...');
          for (const publicId of publicIds) {
            try {
              await cloudinary.uploader.destroy(publicId);
              totalDeleted++;
              console.log(`✅ 删除成功: ${publicId}`);
            } catch (singleError) {
              console.error(`❌ 删除失败: ${publicId} - ${singleError.message}`);
            }
          }
        }
      }

      // 检查是否还有更多文件
      hasMore = result.next_cursor ? true : false;
      nextCursor = result.next_cursor;

      if (hasMore) {
        console.log('🔄 继续获取更多文件...');
        // 添加短暂延迟避免 API 限制
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 删除文件夹
    console.log('📂 开始删除文件夹...');
    try {
      const folders = await cloudinary.api.root_folders();
      if (folders.folders && folders.folders.length > 0) {
        for (const folder of folders.folders) {
          try {
            await cloudinary.api.delete_folder(folder.name);
            console.log(`✅ 删除文件夹: ${folder.name}`);
          } catch (folderError) {
            console.error(`❌ 删除文件夹失败: ${folder.name} - ${folderError.message}`);
          }
        }
      }
    } catch (folderListError) {
      console.error('❌ 获取文件夹列表失败:', folderListError.message);
    }

    console.log(`\n🎉 删除完成！总计删除了 ${totalDeleted} 个文件`);

  } catch (error) {
    console.error('❌ 删除过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 安全确认
function confirmDeletion() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('⚠️  警告: 此操作将删除 Cloudinary 中的所有文件！');
    console.log('⚠️  此操作不可逆转！');
    rl.question('确定要继续吗？输入 "DELETE" 确认: ', (answer) => {
      rl.close();
      resolve(answer === 'DELETE');
    });
  });
}

async function main() {
  // 检查环境变量
  if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
    console.error('❌ 请设置 Cloudinary 环境变量:');
    console.error('   CLOUDINARY_CLOUD_NAME');
    console.error('   CLOUDINARY_API_KEY');
    console.error('   CLOUDINARY_API_SECRET');
    process.exit(1);
  }

  console.log(`🔗 连接到 Cloudinary: ${process.env.CLOUDINARY_CLOUD_NAME}`);

  // 安全确认
  const confirmed = await confirmDeletion();
  if (!confirmed) {
    console.log('❌ 操作已取消');
    process.exit(0);
  }

  await deleteAllCloudinaryFiles();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { deleteAllCloudinaryFiles };
