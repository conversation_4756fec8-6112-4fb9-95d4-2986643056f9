'use client';

import { useState } from 'react';
import CommentsSection from '@/components/comments/CommentsSection';
import Container from '@/components/ui/container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestCommentsPage() {
  // 使用一个测试用的文章ID
  const testPostId = 'test-post-id';

  return (
    <Container>
      <div className="max-w-4xl mx-auto py-8">
        {/* 模拟博客文章 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>测试博客文章</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              这是一篇用于测试评论系统的模拟博客文章。您可以在下方的评论区测试评论功能。
            </p>
            <p className="text-gray-600">
              评论系统包含以下功能：
            </p>
            <ul className="list-disc list-inside text-gray-600 mt-2 space-y-1">
              <li>提交评论和回复</li>
              <li>内容安全验证</li>
              <li>垃圾评论检测</li>
              <li>评论审核机制</li>
              <li>嵌套回复支持</li>
            </ul>
          </CardContent>
        </Card>

        {/* 评论区 */}
        <CommentsSection 
          postId={testPostId}
          commentsEnabled={true}
        />
      </div>
    </Container>
  );
}
